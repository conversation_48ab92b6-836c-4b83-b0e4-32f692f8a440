import requests

from components.tasq.agentic_pipeline.core.config import settings

TINYBIRD_API_KEY = settings.TINYBIRD_API_KEY
TINYBIRD_FULL_URL = settings.TINYBIRD_FULL_URL


def data_fetcher(sql_query: str):
    headers = {"Authorization": f"Bearer {TINYBIRD_API_KEY}", "Accept-Encoding": "gzip"}
    request_data = {"q": sql_query}
    response = requests.get(
        TINYBIRD_FULL_URL,
        data=request_data,
        headers=headers,
    )
    return response
