from datetime import datetime

import matplotlib.patches as mpatches
import matplotlib.pyplot as plt
import numpy as np
import pandas as pd
from matplotlib.gridspec import GridSpec
from matplotlib.patches import Rectangle


class WellProductionReportGenerator:
    def __init__(self):
        self.COLORS = {
            "primary": "#2E86AB",
            "secondary": "#A23B72",
            "accent": "#F18F01",
            "success": "#C73E1D",
            "info": "#6A994E",
            "background": "#F5F7FA",
            "text": "#2C3E50",
            "gradient_start": "#667eea",
            "gradient_end": "#764ba2",
        }

        self.PASTEL_COLORS = [
            "#FF6B6B",
            "#4ECDC4",
            "#45B7D1",
            "#96CEB4",
            "#FFEAA7",
            "#DDA0DD",
            "#98D8C8",
            "#F7DC6F",
        ]

        self.MATPLOTLIB_CONFIG = {
            "font.family": "serif",
            "font.serif": ["Times New Roman"],
            "font.size": 12,
            "axes.linewidth": 1.0,
            "grid.alpha": 0.3,
            "figure.facecolor": "white",
            "axes.facecolor": "white",
            "axes.labelsize": 12,
            "axes.titlesize": 14,
            "xtick.labelsize": 10,
            "ytick.labelsize": 10,
        }

        self.PAGE_SIZE = (8.27, 11.69)
        self.FONT_SIZES = {
            "title": 32,
            "subtitle": 24,
            "section_title": 16,
            "chart_title": 14,
            "body": 12,
            "label": 11,
            "small": 10,
        }

        self.CHART_CONFIG = {
            "bar_width": 0.35,
            "bar_height": 0.6,
            "alpha": 0.8,
            "edge_color": "white",
            "line_width": 2,
            "explode_factor": 0.08,
            "grid_alpha": 0.3,
        }

    def setup_matplotlib_style(self):
        plt.style.use("seaborn-v0_8-whitegrid")
        plt.rcParams.update(self.MATPLOTLIB_CONFIG)

    def create_title_page(self, pdf_pages, well_name):
        fig = plt.figure(figsize=self.PAGE_SIZE)
        fig.patch.set_facecolor("white")

        ax = fig.add_subplot(111)
        ax.set_xlim(0, 10)
        ax.set_ylim(0, 15)
        ax.axis("off")

        circle1 = plt.Circle((2, 12), 1.2, color=self.COLORS["primary"], alpha=0.1)
        circle2 = plt.Circle((8, 3), 1.5, color=self.COLORS["accent"], alpha=0.1)
        ax.add_patch(circle1)
        ax.add_patch(circle2)

        ax.text(
            5,
            10.5,
            "WELL PRODUCTION REPORT",
            fontsize=self.FONT_SIZES["title"],
            fontweight="bold",
            ha="center",
            color=self.COLORS["primary"],
        )

        rect = Rectangle(
            (1.5, 7.8),
            7,
            1.8,
            linewidth=self.CHART_CONFIG["line_width"],
            edgecolor=self.COLORS["secondary"],
            facecolor=self.COLORS["background"],
            alpha=self.CHART_CONFIG["alpha"],
        )
        ax.add_patch(rect)
        ax.text(
            5, 8.7, f"{well_name}", fontsize=20, fontweight="bold", ha="center", color=self.COLORS["text"]
        )

        ax.plot([2.5, 7.5], [6.2, 6.2], color=self.COLORS["accent"], linewidth=3)
        ax.text(
            5,
            5.4,
            f'Report Generated: {datetime.now().strftime("%B %d, %Y")}',
            fontsize=self.FONT_SIZES["chart_title"],
            ha="center",
            color=self.COLORS["text"],
        )

        ax.text(
            5,
            2.5,
            "Operational Excellence Through Data Intelligence",
            fontsize=self.FONT_SIZES["section_title"],
            ha="center",
            style="italic",
            color=self.COLORS["secondary"],
        )

        pdf_pages.savefig(fig, bbox_inches="tight", facecolor="white")
        plt.close()

    def create_production_overview(self, pdf_pages, deferment_data):
        if not deferment_data or len(deferment_data) == 0:
            return

        df = pd.DataFrame(deferment_data)

        fig = plt.figure(figsize=self.PAGE_SIZE)
        gs = GridSpec(
            3,
            2,
            height_ratios=[0.4, 2.5, 2.5],
            width_ratios=[1, 1],
            hspace=0.4,
            wspace=0.4,
            top=0.95,
            bottom=0.08,
            left=0.1,
            right=0.95,
        )

        ax_title = fig.add_subplot(gs[0, :])
        ax_title.text(
            0.5,
            0.5,
            "PRODUCTION & DEFERMENT ANALYSIS",
            fontsize=self.FONT_SIZES["subtitle"],
            fontweight="bold",
            ha="center",
            va="center",
            color=self.COLORS["primary"],
        )
        ax_title.axis("off")
        ax1 = fig.add_subplot(gs[1, 0])
        self._create_production_deferment_chart(ax1, df)

        ax2 = fig.add_subplot(gs[1, 1])
        self._create_well_status_pie_chart(ax2, df)

        ax3 = fig.add_subplot(gs[2, :])
        self._create_efficiency_chart(ax3, df)

        plt.tight_layout()
        pdf_pages.savefig(fig, bbox_inches="tight", facecolor="white")
        plt.close()

    def _create_production_deferment_chart(self, ax, df):
        summary = df.groupby("deferment_type").agg({"production": "sum", "deferment": "sum"}).reset_index()

        x_value = np.arange(len(summary))

        bars1 = ax.bar(
            x_value - self.CHART_CONFIG["bar_width"] / 2,
            summary["production"],
            self.CHART_CONFIG["bar_width"],
            label="Production",
            color=self.COLORS["info"],
            alpha=self.CHART_CONFIG["alpha"],
        )
        bars2 = ax.bar(
            x_value + self.CHART_CONFIG["bar_width"] / 2,
            summary["deferment"],
            self.CHART_CONFIG["bar_width"],
            label="Deferment",
            color=self.COLORS["success"],
            alpha=self.CHART_CONFIG["alpha"],
        )

        ax.set_xlabel("Deferment Type", fontweight="bold", fontsize=self.FONT_SIZES["body"])
        ax.set_ylabel("Rate (BOE)", fontweight="bold", fontsize=self.FONT_SIZES["body"])
        ax.set_title(
            "Production vs Deferment by Type",
            fontweight="bold",
            color=self.COLORS["text"],
            fontsize=self.FONT_SIZES["chart_title"],
            pad=20,
        )
        ax.set_xticks(x_value)
        ax.set_xticklabels(
            [t.replace("_", "\n") for t in summary["deferment_type"]],
            rotation=0,
            fontsize=self.FONT_SIZES["small"],
        )
        ax.legend(frameon=True, fancybox=True, shadow=True, fontsize=self.FONT_SIZES["label"])
        ax.grid(True, alpha=self.CHART_CONFIG["grid_alpha"])

        self._add_bar_labels(ax, bars1)
        self._add_bar_labels(ax, bars2)

    def _create_well_status_pie_chart(self, ax, df):
        status_counts = df["well_status"].value_counts()

        wedges, texts, autotexts = ax.pie(
            status_counts.values,
            labels=status_counts.index,
            autopct="%1.1f%%",
            startangle=90,
            colors=self.PASTEL_COLORS[: len(status_counts)],
            explode=[self.CHART_CONFIG["explode_factor"]] * len(status_counts),
            shadow=True,
            textprops={"fontsize": self.FONT_SIZES["label"]},
        )

        ax.set_title(
            "Well Status Distribution",
            fontweight="bold",
            color=self.COLORS["text"],
            fontsize=self.FONT_SIZES["chart_title"],
            pad=20,
        )

        for text in texts:
            text.set_fontweight("bold")
            text.set_fontsize(self.FONT_SIZES["label"])
        for autotext in autotexts:
            autotext.set_color("white")
            autotext.set_fontweight("bold")
            autotext.set_fontsize(self.FONT_SIZES["small"])

    def _create_efficiency_chart(self, ax, df):
        df["efficiency"] = df["production"] / (df["production"] + df["deferment"]) * 100
        df["efficiency"] = df["efficiency"].fillna(0)

        y_pos = np.arange(len(df))
        bars = ax.barh(
            y_pos,
            df["efficiency"],
            height=self.CHART_CONFIG["bar_height"],
            color=[self.PASTEL_COLORS[i % len(self.PASTEL_COLORS)] for i in range(len(df))],
            alpha=self.CHART_CONFIG["alpha"],
            edgecolor=self.CHART_CONFIG["edge_color"],
            linewidth=self.CHART_CONFIG["line_width"],
        )

        ax.set_yticks(y_pos)
        ax.set_yticklabels(
            [
                f"{row['deferment_type'].replace('_', ' ').title()}\n({row['well_status']})"
                for _, row in df.iterrows()
            ],
            fontsize=self.FONT_SIZES["label"],
        )
        ax.set_xlabel("Production Efficiency (%)", fontweight="bold", fontsize=self.FONT_SIZES["body"])
        ax.set_title(
            "Production Efficiency by Deferment Type & Status",
            fontweight="bold",
            color=self.COLORS["text"],
            fontsize=self.FONT_SIZES["chart_title"],
            pad=20,
        )
        ax.grid(True, alpha=self.CHART_CONFIG["grid_alpha"], axis="x")
        ax.set_xlim(0, max(df["efficiency"]) * 1.15)

        for _, side_bar in enumerate(bars):
            width = side_bar.get_width()
            ax.text(
                width + max(df["efficiency"]) * 0.02,
                side_bar.get_y() + side_bar.get_height() / 2,
                f"{width:.1f}%",
                ha="left",
                va="center",
                fontweight="bold",
                fontsize=self.FONT_SIZES["small"],
            )

    def create_operational_status_page(self, pdf_pages, event_data):
        """Create operational status visualization with task timeline."""
        if not event_data or (isinstance(event_data, tuple) and len(event_data) == 0):
            return

        fig = plt.figure(figsize=self.PAGE_SIZE)
        gs = GridSpec(
            3, 1, height_ratios=[0.4, 2.8, 2.8], hspace=0.5, top=0.95, bottom=0.08, left=0.1, right=0.95
        )

        ax_title = fig.add_subplot(gs[0])
        ax_title.text(
            0.5,
            0.5,
            "OPERATIONAL STATUS & EVENTS",
            fontsize=self.FONT_SIZES["subtitle"],
            fontweight="bold",
            ha="center",
            va="center",
            color=self.COLORS["primary"],
        )
        ax_title.axis("off")

        if isinstance(event_data, tuple) and len(event_data) > 0:
            event_df = pd.DataFrame(event_data[0].get("data", []))

            if not event_df.empty:
                # Task Timeline
                ax1 = fig.add_subplot(gs[1])
                self._create_task_timeline(ax1, event_df)

        ax2 = fig.add_subplot(gs[2])
        self._create_status_dashboard(ax2, event_data)

        plt.tight_layout()
        pdf_pages.savefig(fig, bbox_inches="tight", facecolor="white")
        plt.close()

    def _create_task_timeline(self, ax, event_df):
        tasks = event_df["task_name"].tolist()
        days_open = event_df["days_open"].tolist()

        colors = []
        for days in days_open:
            if days > 30:
                colors.append(self.COLORS["success"])  # Red for urgent
            elif days > 14:
                colors.append(self.COLORS["accent"])  # Orange for moderate
            else:
                colors.append(self.COLORS["info"])  # Green for new

        ax.set_yticks(range(len(tasks)))
        ax.set_yticklabels(
            [task[:40] + "..." if len(task) > 40 else task for task in tasks],
            fontsize=self.FONT_SIZES["label"],
        )
        ax.set_xlabel("Days Open", fontweight="bold", fontsize=self.FONT_SIZES["body"])
        ax.set_title(
            "Active Tasks Timeline",
            fontweight="bold",
            color=self.COLORS["text"],
            fontsize=self.FONT_SIZES["chart_title"],
            pad=20,
        )
        ax.grid(True, alpha=self.CHART_CONFIG["grid_alpha"], axis="x")
        ax.set_xlim(0, max(days_open) * 1.2)

        for index, days in enumerate(days_open):
            ax.text(
                days + max(days_open) * 0.02,
                index,
                f"{days}d",
                va="center",
                fontweight="bold",
                color=self.COLORS["text"],
                fontsize=self.FONT_SIZES["small"],
            )

        urgent_patch = mpatches.Patch(color=self.COLORS["success"], label="Urgent (>30 days)")
        moderate_patch = mpatches.Patch(color=self.COLORS["accent"], label="Moderate (14-30 days)")
        new_patch = mpatches.Patch(color=self.COLORS["info"], label="New (<14 days)")
        ax.legend(
            handles=[urgent_patch, moderate_patch, new_patch],
            loc="lower right",
            frameon=True,
            fancybox=True,
            shadow=True,
            fontsize=self.FONT_SIZES["label"],
        )

    def _create_status_dashboard(self, ax, event_data):
        ax.axis("off")
        ax.set_title(
            "System Status Dashboard",
            fontweight="bold",
            color=self.COLORS["text"],
            fontsize=self.FONT_SIZES["section_title"],
            y=0.95,
        )

        if isinstance(event_data, tuple) and len(event_data) > 0 and event_data[0].get("data"):
            total_tasks = len(event_data[0]["data"])
            avg_days = (
                sum(task["days_open"] for task in event_data[0]["data"]) / total_tasks
                if total_tasks > 0
                else 0
            )

            cards_data = [
                {"title": "Active Tasks", "value": str(total_tasks), "color": self.COLORS["primary"]},
                {"title": "Avg Days Open", "value": f"{avg_days:.1f}", "color": self.COLORS["secondary"]},
                {"title": "System Status", "value": "ONLINE", "color": self.COLORS["info"]},
                {
                    "title": "Last Update",
                    "value": datetime.now().strftime("%H:%M"),
                    "color": self.COLORS["accent"],
                },
            ]

            for index, card in enumerate(cards_data):
                x_val = (index % 2) * 0.45 + 0.05
                y_val = 0.5 if index < 2 else 0.05

                rect = Rectangle(
                    (x_val, y_val),
                    0.4,
                    0.35,
                    facecolor=card["color"],
                    alpha=0.1,
                    edgecolor=card["color"],
                    linewidth=self.CHART_CONFIG["line_width"],
                )
                ax.add_patch(rect)

                ax.text(
                    x_val + 0.2,
                    y_val + 0.25,
                    card["value"],
                    ha="center",
                    va="center",
                    fontsize=self.FONT_SIZES["subtitle"],
                    fontweight="bold",
                    color=card["color"],
                )
                ax.text(
                    x_val + 0.2,
                    y_val + 0.08,
                    card["title"],
                    ha="center",
                    va="center",
                    fontsize=self.FONT_SIZES["body"],
                    color=self.COLORS["text"],
                )

        ax.set_xlim(0, 1)
        ax.set_ylim(0, 1)

    def create_summary_page(self, pdf_pages, original_data):
        fig = plt.figure(figsize=self.PAGE_SIZE)
        gs = GridSpec(
            4,
            2,
            height_ratios=[0.4, 1.5, 2, 1.5],
            hspace=0.5,
            wspace=0.4,
            top=0.95,
            bottom=0.08,
            left=0.1,
            right=0.95,
        )

        ax_title = fig.add_subplot(gs[0, :])
        ax_title.text(
            0.5,
            0.5,
            "EXECUTIVE SUMMARY",
            fontsize=self.FONT_SIZES["subtitle"],
            fontweight="bold",
            ha="center",
            va="center",
            color=self.COLORS["primary"],
        )
        ax_title.axis("off")

        ax1 = fig.add_subplot(gs[1, :])
        self._create_kpi_section(ax1, original_data)

        ax2 = fig.add_subplot(gs[2, 0])
        self._create_data_quality_chart(ax2)

        ax3 = fig.add_subplot(gs[2, 1])
        self._create_recommendations_section(ax3)

        ax4 = fig.add_subplot(gs[3, :])
        self._create_operational_notes(ax4)

        plt.tight_layout()
        pdf_pages.savefig(fig, bbox_inches="tight", facecolor="white")
        plt.close()

    def _create_kpi_section(self, ax, original_data):
        ax.axis("off")
        ax.set_title(
            "Key Performance Indicators",
            fontweight="bold",
            color=self.COLORS["text"],
            fontsize=self.FONT_SIZES["section_title"],
            y=0.95,
        )

        deferment_data = original_data.get("deferment_well_data", {}).get("data", [])
        if deferment_data:
            df = pd.DataFrame(deferment_data)
            total_production = df["production"].sum()
            total_deferment = df["deferment"].sum()
            overall_efficiency = (
                (total_production / (total_production + total_deferment)) * 100
                if (total_production + total_deferment) > 0
                else 0
            )

            metrics = [
                {
                    "title": "Total Production",
                    "value": f"{total_production:.1f}",
                    "unit": "BOE",
                    "color": self.COLORS["info"],
                },
                {
                    "title": "Total Deferment",
                    "value": f"{total_deferment:.1f}",
                    "unit": "BOE",
                    "color": self.COLORS["success"],
                },
                {
                    "title": "Overall Efficiency",
                    "value": f"{overall_efficiency:.1f}",
                    "unit": "%",
                    "color": self.COLORS["primary"],
                },
                {
                    "title": "Data Points",
                    "value": str(len(df)),
                    "unit": "Records",
                    "color": self.COLORS["accent"],
                },
            ]

            for index, metric in enumerate(metrics):
                x_vals = (index % 4) * 0.25 + 0.05
                y_vals = 0.2

                circle = plt.Circle((x_vals + 0.125, y_vals + 0.5), 0.08, color=metric["color"], alpha=0.2)
                ax.add_patch(circle)

                ax.text(
                    x_vals + 0.125,
                    y_vals + 0.5,
                    metric["value"],
                    ha="center",
                    va="center",
                    fontsize=self.FONT_SIZES["section_title"],
                    fontweight="bold",
                    color=metric["color"],
                )
                ax.text(
                    x_vals + 0.125,
                    y_vals + 0.15,
                    f"{metric['title']}\n({metric['unit']})",
                    ha="center",
                    va="center",
                    fontsize=self.FONT_SIZES["label"],
                    color=self.COLORS["text"],
                )

        ax.set_xlim(0, 1)
        ax.set_ylim(0, 1)

    def _create_data_quality_chart(self, ax):
        quality_metrics = ["Completeness", "Accuracy", "Timeliness", "Consistency"]
        quality_scores = [95, 88, 92, 90]  # Simulated scores

        bars = ax.bar(
            quality_metrics,
            quality_scores,
            width=self.CHART_CONFIG["bar_height"],
            color=[self.PASTEL_COLORS[i] for i in range(len(quality_metrics))],
            alpha=self.CHART_CONFIG["alpha"],
            edgecolor=self.CHART_CONFIG["edge_color"],
            linewidth=self.CHART_CONFIG["line_width"],
        )

        ax.set_ylabel("Score (%)", fontweight="bold", fontsize=self.FONT_SIZES["body"])
        ax.set_title(
            "Data Quality Assessment",
            fontweight="bold",
            color=self.COLORS["text"],
            fontsize=self.FONT_SIZES["chart_title"],
            pad=20,
        )
        ax.set_ylim(0, 100)
        ax.grid(True, alpha=self.CHART_CONFIG["grid_alpha"], axis="y")
        ax.tick_params(axis="x", rotation=45)

        for side_bar, score in zip(bars, quality_scores):
            ax.text(
                side_bar.get_x() + side_bar.get_width() / 2,
                side_bar.get_height() + 2,
                f"{score}%",
                ha="center",
                va="bottom",
                fontweight="bold",
                fontsize=self.FONT_SIZES["small"],
            )

    def _create_recommendations_section(self, ax):
        ax.axis("off")
        recommendations = """KEY INSIGHTS:

        ✓ Production efficiency within
        acceptable ranges

        ✓ Real-time monitoring active

        ✓ Review extended deferments
        (>30 days recommended)

        ✓ Data quality metrics indicate
        reliable infrastructure

        NEXT ACTIONS:
        • Schedule maintenance review
        • Optimize lift system operations
        • Implement predictive analytics
        """

        ax.text(
            0.05,
            0.95,
            recommendations,
            transform=ax.transAxes,
            fontsize=self.FONT_SIZES["label"],
            verticalalignment="top",
            bbox=dict(
                boxstyle="round,pad=0.6",
                facecolor=self.COLORS["background"],
                edgecolor=self.COLORS["primary"],
                linewidth=self.CHART_CONFIG["line_width"],
                alpha=0.9,
            ),
        )

    def _create_operational_notes(self, ax):
        ax.axis("off")
        notes_text = f"""
        OPERATIONAL NOTES: All active tasks are monitored for timely completion. System performance indicators
        are within normal parameters. Automated alerts configured for critical threshold breaches. Data refresh
        cycle operates with real-time monitoring at 5-minute intervals.

        Report Generated: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")} | Confidential Document
        """

        ax.text(
            0.05,
            0.8,
            notes_text,
            transform=ax.transAxes,
            fontsize=self.FONT_SIZES["label"],
            verticalalignment="top",
            bbox=dict(
                boxstyle="round,pad=0.5",
                facecolor=self.COLORS["background"],
                alpha=self.CHART_CONFIG["alpha"],
            ),
            wrap=True,
        )

    def _add_bar_labels(self, ax, bars):
        for side_bar in bars:
            height = side_bar.get_height()
            ax.text(
                side_bar.get_x() + side_bar.get_width() / 2.0,
                height + height * 0.02,
                f"{height:.1f}",
                ha="center",
                va="bottom",
                fontweight="bold",
                fontsize=self.FONT_SIZES["small"],
            )
