import os
from logging import Logger
from typing import Optional

from dotenv import load_dotenv
from pydantic_settings import BaseSettings

from components.tasq.agentic_pipeline.services.logging_service import get_logger

load_dotenv()


class Settings(BaseSettings):
    APPLICATION_SETUP: str = os.getenv("APPLICATION_SETUP", "fastapp")
    # ENVIRONMENT VARIABLES
    EXECUTION_ENVIRONMENT: str = os.getenv("EXECUTION_ENVIRONMENT",
                                           "DEVELOPMENT")
    # TINYBIRD CREDS
    TINYBIRD_URL: str = os.getenv("TINYBIRD_URL")
    TINYBIRD_FULL_URL: str = 'https://' + TINYBIRD_URL
    DB_URI: str = os.getenv("DB_URI")
    TINYBIRD_API_KEY: str = os.getenv("TINYBIRD_API_KEY")

    LLM_MODEL_NAME: str = os.getenv("LLM_MODEL_NAME")
    # Signal Search
    RECOMMENDATION_URL: str = os.getenv("RECOMMENDATION_URL",
                                        "ai.tasq.io/ai/recommendations")
    REFRESH_TOKEN: str = os.getenv("REFRESH_TOKEN")
    TIME_SERIES_URL: str = os.getenv("TIME_SERIES_URL",
                                     "rocksetapi-dev.tasqinc.com/dev/ai/timeseries-search")
    SIMILAR_URL: str = os.getenv("SIMILAR_URL",
                                 "rocksetapi-dev.tasqinc.com/dev/ai/similar")
    REFRESH_TOKEN_URL: str = os.getenv("REFRESH_TOKEN_URL")

    PDF_REPORT_DIR: str = os.getenv("REPORTS_DIR", "Reports")
    APP_PORT: int = os.getenv("APP_PORT", 8000)
    API_KEY: str = os.getenv("API_KEY")
    LOGS_DIR: str = os.getenv("LOGS_DIR", "Logs")
    # GOOGLE CREDS
    MAX_ITER: int = os.getenv("MAX_ITER", 3)
    # VANNA CREDS
    APP: str = os.getenv("SESSION_APP_NAME", "tasq_app")
    VANNA_MODEL_NAME: str = os.getenv("VANNA_MODEL_NAME")
    VANNA_API_KEY: str = os.getenv("VANNA_API_KEY")
    VANNA_LLM_MODEL: str = os.getenv("VANNA_LLM_MODEL")
    LOGGER: Optional[Logger] = None
    SCHEMA_FILE: str = os.getenv("SCHEMA_FILE",
                                 "assets/tasq-agent_sql refinement loop_schema_draft_02052025_2.json")

    OPEN_ROUTER_BASE: str = os.getenv("OPENROUTER_BASE")
    OPEN_ROUTER_KEY: str = os.getenv("OPENROUTER_API_KEY")

    AWS_SECRET_KEY: str = os.getenv("AWS_SECRET_ACCESS_KEY")
    AWS_ACCESS_KEY: str = os.getenv("AWS_ACCESS_KEY_ID")
    AWS_REGION: str = os.getenv("AWS_DEFAULT_REGION")
    S3_BUCKET_NAME: str = os.getenv("S3_BUCKET_NAME")

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.LOGGER = get_logger(log_dir=self.LOGS_DIR)

    class Config:
        case_sensitive = True


settings = Settings()
