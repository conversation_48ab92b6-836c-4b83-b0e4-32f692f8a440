from contextlib import asynccontextmanager
from functools import lru_cache

from components.tasq.agentic_pipeline.core.config import settings
from components.tasq.agentic_pipeline.database.abstract import BaseSessionManager
from components.tasq.agentic_pipeline.database.dependency import DatabaseDependency


async def get_db_instance() -> BaseSessionManager:
    """
    FastAPI dependency function to inject database instances.
    """
    return await DatabaseDependency.get_instance()


# Cached singleton for default database (common case optimization)
@lru_cache(maxsize=1)
def get_default_db_sync() -> BaseSessionManager:
    """
    Get default database instance synchronously (for non-async contexts).
    """
    return DatabaseDependency.get_instance_sync()


@asynccontextmanager
async def lifespan(app):
    """
    FastAPI lifespan context manager for initialization and cleanup.
    Use with: app = FastAPI(lifespan=lifespan)
    """
    db_uri = settings.DB_URI

    await DatabaseDependency.initialize(db_uri)
    yield
