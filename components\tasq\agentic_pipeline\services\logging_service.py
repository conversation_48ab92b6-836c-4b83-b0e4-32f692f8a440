import logging
import os
from datetime import datetime


def get_logger(name="app_logger", log_dir="", level=logging.DEBUG):
    if not os.path.exists(log_dir):
        os.makedirs(log_dir)

    date_str = datetime.now().strftime("%Y-%m-%d")
    log_file = os.path.join(log_dir, f"{date_str}.log")

    logger = logging.getLogger(name)
    logger.setLevel(level)

    if not logger.handlers:
        fmt = "[%(asctime)s] %(levelname)s in %(module)s: %(message)s"
        formatter = logging.Formatter(fmt)

        ch = logging.StreamHandler()
        ch.setFormatter(formatter)
        logger.addHandler(ch)

        fh = logging.FileHandler(log_file, mode="a")
        fh.setFormatter(formatter)
        logger.addHandler(fh)

    return logger
