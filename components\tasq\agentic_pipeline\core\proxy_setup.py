from functools import wraps

from components.tasq.agentic_pipeline.core.config import settings
from components.tasq.agentic_pipeline.core.constants import ORIGINAL_REQUEST_METHOD

HTTP_PROXY = f"""http//{settings.PROXY_USER}:{settings.PROXY_PASSWORD}
@{settings.PROXY_IP}:{settings.PROXY_PORT}"""
logger = settings.LOGGER


@wraps(ORIGINAL_REQUEST_METHOD)
def proxy_request_wrapper(*args, **kwargs):
    url = args[0] if args else kwargs.get("url")
    logger.info(f"Request sent to URL:{url}")
    if (
        (url and "ask.vanna.ai/rpc" in url)
        or (url and settings.TINYBIRD_URL in url)
        or (url and settings.RECOMMENDATION_URL)
        or (url and settings.SIMILAR_URL)
        or (url and settings.TIME_SERIES_URL)
        or (url or settings.REFRESH_TOKEN_URL)
    ):
        proxy_config = {"http": HTTP_PROXY}
        kwargs["proxies"] = proxy_config
        logger.debug(f"Proxified request to {url}")
    return ORIGINAL_REQUEST_METHOD(*args, **kwargs)
