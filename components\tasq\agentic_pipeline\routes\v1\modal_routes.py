import json
import modal
import os

app = modal.App("tasq-agentic-pipeline")

db_volume = modal.Volume.from_name("tasq-db-storage", create_if_missing=True)
base_dir = os.path.abspath(
    os.path.join(os.path.dirname(__file__), "../../../../../")
)
image = (
    modal.Image.debian_slim(python_version="3.11")
    .pip_install(['pydantic[email]', 'fastapi_sso'])
    .pip_install_from_requirements(f"{base_dir}/components/tasq/agentic_pipeline/requirements.txt")
    .add_local_dir(base_dir, "/tasq"))

secrets = [
    modal.Secret.from_name("my-secret")]


@app.function(
    image=image,
    secrets=secrets,
    cpu=4,
    memory=8000,
    volumes={"/shared_db": db_volume},
    keep_warm=3,
    container_idle_timeout=3600,
    concurrency_limit=20
)
@modal.asgi_app()
def serve():
    import os
    import sys
    os.environ['STAGE'] = 'dev'
    sys.path.append("/tasq")

    assets_dir = "/tasq/components/tasq/agentic_pipeline/assets"
    os.makedirs(assets_dir, exist_ok=True)

    shared_db_dir = "/shared_db/session_data"
    os.makedirs(shared_db_dir, exist_ok=True)

    os.environ['DB_URI'] = f"shelve://{shared_db_dir}?session_file=sessions.db&message_file=messages.db&user_file=users.db"
    os.environ['MAX_ITER'] = "1"
    schema_file = f"{assets_dir}/tasq-agent_sql refinement loop_schema_draft_02052025_2.json"
    if not os.path.exists(schema_file):
        default_schema = {}
        with open(schema_file, "w") as f:
            json.dump(default_schema, f)
    from bases.tasq.agentic_pipeline.core import app as fastapi_app
    return fastapi_app


@app.local_entrypoint()
def main():
    """Local development entry point"""
    serve.remote()
