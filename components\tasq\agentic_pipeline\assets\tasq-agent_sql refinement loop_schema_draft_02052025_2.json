{"alarms_v2": {"description": "Records alarm events, linking alarm details (value) to specific nodes (nodeid), operators, and timestamps (time). This table tracks when specific conditions triggered an alarm for monitoring purposes.", "columns": [{"name": "_event_time", "type": "DateTime64(3)", "description": "Timestamp indicating when the alarm event record was ingested or processed by the system."}, {"name": "value", "type": "String", "description": "The details or message associated with the alarm event (e.g., 'High Pressure Threshold Exceeded')."}, {"name": "_id", "type": "String", "description": "Unique identifier for this specific alarm event record."}, {"name": "nodeid", "type": "String", "description": "Identifier of the node (e.g., well, sensor, equipment) that triggered the alarm. Links to node_meta._id."}, {"name": "operator", "type": "String", "description": "Identifier of the operator associated with the node. Links to operator_meta._id."}, {"name": "time", "type": "DateTime64(3)", "description": "The actual timestamp when the alarm condition occurred at the source."}], "relationships": [{"column": "nodeid", "references_table": "node_meta", "references_column": "_id", "description": "Links the alarm event to the specific node it originated from."}, {"column": "operator", "references_table": "operator_meta", "references_column": "_id", "description": "Links the alarm event to the operator responsible for the node."}]}, "categorized_comments": {"description": "Stores categorized comments, notes, or events (e.g., 'waiting on', 'retrained') related to specific nodes/wells. Used for tracking operational history, status updates, maintenance logs, and categorization of events.", "columns": [{"name": "_event_time", "type": "DateTime64(6)", "description": "System timestamp indicating when the comment record was created or last updated."}, {"name": "_id", "type": "String", "description": "Unique identifier for the comment or event record."}, {"name": "body", "type": "Nullable(String)", "description": "The main textual content of the comment, note, or event description."}, {"name": "created_by", "type": "Nullable(String)", "description": "Identifier (e.g., username, system process) of who created the comment."}, {"name": "form", "type": "<PERSON><PERSON><PERSON>(JSON)", "description": "Stores structured data associated with the comment, potentially from a form submission (e.g., key-value pairs)."}, {"name": "nodeid", "type": "String", "description": "Identifier of the node (e.g., well, pad) this comment pertains to. Links to node_meta._id."}, {"name": "operator", "type": "String", "description": "Identifier of the operator associated with the node. Links to operator_meta._id."}, {"name": "point_time", "type": "DateTime64(3)", "description": "The primary timestamp representing when the event or situation described in the comment occurred or became relevant."}, {"name": "type", "type": "String", "description": "The general type of the entry (e.g., 'comment', 'log', 'status_change', 'maintenance')."}, {"name": "vector", "type": "Array(Nullable(Float32))", "description": "Numerical vector representation (embedding) of the comment text, used for similarity searches or machine learning tasks."}, {"name": "vector_text", "type": "Nullable(String)", "description": "The specific text content that was used to generate the vector."}, {"name": "summary", "type": "Nullable(String)", "description": "A brief summary of the comment or event."}, {"name": "end_date", "type": "Nullable(DateTime64(3))", "description": "Timestamp indicating the end of the relevance period for this comment/event, if applicable."}, {"name": "start_date", "type": "Nullable(DateTime)", "description": "Timestamp indicating the start of the relevance period for this comment/event, if applicable. (Note: Might be redundant with point_time)."}, {"name": "delete_flag", "type": "UInt8", "description": "Flag indicating if the record is marked for deletion (soft delete). 0 = active, 1 = deleted."}, {"name": "tracking_id", "type": "Nullable(String)", "description": "An identifier used to link this comment to other related items (e.g., a work order ID, a support ticket number)."}, {"name": "category", "type": "Nullable(String)", "description": "The primary category assigned to the comment/event (e.g., 'Maintenance', 'Operational Issue', 'Safety')."}, {"name": "platform", "type": "Nullable(String)", "description": "The platform or system from which the comment originated (e.g., 'Web UI', 'Mobile App', 'SCADA')."}, {"name": "categorization_summary", "type": "Nullable(String)", "description": "An automatically generated summary based on the assigned categories."}, {"name": "sub_category", "type": "Nullable(String)", "description": "A more specific sub-category branching from the main 'category'."}, {"name": "action_taken", "type": "Nullable(String)", "description": "Description of the action taken in response to the event or as part of the comment."}, {"name": "sub_category_2", "type": "Nullable(String)", "description": "A second level sub-category for finer granularity."}, {"name": "action_taken_2", "type": "Nullable(String)", "description": "A secondary action taken."}, {"name": "newValue", "type": "Nullable(String)", "description": "Used for tracking changes, storing the new value of a field."}, {"name": "oldValue", "type": "Nullable(String)", "description": "Used for tracking changes, storing the previous value of a field."}, {"name": "time", "type": "Nullable(String)", "description": "An additional timestamp field, potentially storing time in a specific string format or for a different purpose than point_time."}, {"name": "is_tasq_closed", "type": "Nullable(String)", "description": "Flag or status indicating if an associated task ('TasQ') is closed."}, {"name": "source_type", "type": "Nullable(String)", "description": "Indicates the source or method through which the comment was generated (e.g., 'Manual Entry', 'Automated Alert')."}, {"name": "level", "type": "Nullable(String)", "description": "Indicates the severity, importance, or level associated with the comment/event."}, {"name": "manually_assigned_tasq", "type": "Nullable(String)", "description": "Flag or status indicating if a related task was assigned manually."}, {"name": "userDefined", "type": "Nullable(UInt8)", "description": "Flag indicating if this comment/event was defined or entered by a user (as opposed to system-generated)."}, {"name": "auto_downtime", "type": "Nullable(UInt8)", "description": "Flag indicating if this event relates to automatically detected downtime."}], "relationships": [{"column": "nodeid", "references_table": "node_meta", "references_column": "_id", "description": "Links the comment to the specific node it relates to."}, {"column": "operator", "references_table": "operator_meta", "references_column": "_id", "description": "Links the comment to the operator associated with the node."}]}, "deferment": {"description": "Contains information about production deferment, which represents periods or amounts of non-production compared to a target or potential. Includes deferment values, types (e.g., 'gas_rate', 'oil_rate'), dates, and links to specific nodes/wells and operators.", "columns": [{"name": "_event_time", "type": "DateTime64(6)", "description": "System timestamp for record creation or update."}, {"name": "_id", "type": "String", "description": "Unique identifier for the deferment record."}, {"name": "date", "type": "Date", "description": "The specific date to which this deferment calculation applies."}, {"name": "deferment", "type": "Nullable(Float64)", "description": "The calculated amount of production deferred (e.g., volume, rate) for the specified date and type."}, {"name": "deferment_type", "type": "String", "description": "The type of production being measured for deferment (e.g., 'gas_rate', 'oil_volume', 'water_cut')."}, {"name": "included", "type": "Nullable(Bool)", "description": "Flag indicating whether this specific deferment record should be included in aggregate reporting or calculations."}, {"name": "key", "type": "String", "description": "A composite or unique key, potentially combining node, date, and type for specific identification or joining purposes."}, {"name": "delete_flag", "type": "UInt8", "description": "Flag indicating if the record is marked for deletion (soft delete). 0 = active, 1 = deleted."}, {"name": "lower_thresh", "type": "Nullable(Float64)", "description": "A lower threshold value potentially used in the deferment calculation logic."}, {"name": "modified_time", "type": "DateTime64(6)", "description": "Timestamp indicating the last time this specific deferment record was modified."}, {"name": "node_id", "type": "String", "description": "Identifier of the node (e.g., well, facility) for which deferment is calculated. Links to node_meta._id."}, {"name": "operator_id", "type": "String", "description": "Identifier of the operator associated with the node. Links to operator_meta._id."}, {"name": "production", "type": "Nullable(Float64)", "description": "The actual production value recorded for this node, date, and type."}, {"name": "reference", "type": "Nullable(Float64)", "description": "The reference or target production value used to calculate deferment (Deferment = Reference - Production)."}, {"name": "upper_thresh", "type": "Nullable(Float64)", "description": "An upper threshold value potentially used in the deferment calculation logic."}, {"name": "elevated_lp", "type": "Nullable(UInt8)", "description": "Flag or indicator related to 'elevated lift performance', potentially influencing deferment categorization."}, {"name": "daily_lp", "type": "Nullable(UInt8)", "description": "Flag or indicator related to 'daily lift performance'."}, {"name": "normal_lp", "type": "Nullable(UInt8)", "description": "Flag or indicator related to 'normal lift performance'."}], "relationships": [{"column": "node_id", "references_table": "node_meta", "references_column": "_id", "description": "Links the deferment record to the specific node it applies to."}, {"column": "operator_id", "references_table": "operator_meta", "references_column": "_id", "description": "Links the deferment record to the responsible operator."}]}, "downtime_predictions": {"description": "Stores predicted downtime events for wells, including predicted start/end times, duration, associated well name, and operator. Used for proactive maintenance and operational planning.", "columns": [{"name": "prediction_id", "type": "String", "description": "Unique identifier for the downtime prediction record."}, {"name": "well_name", "type": "String", "description": "Name of the well for which downtime is predicted. Potentially links to node_meta.name where type='Well'."}, {"name": "node_id", "type": "String", "description": "Identifier of the node (well) associated with the prediction. Links to node_meta._id."}, {"name": "operator_id", "type": "String", "description": "Identifier of the operator associated with the well. Links to operator_meta._id."}, {"name": "downtime_start", "type": "DateTime", "description": "The predicted start date and time of the downtime event."}, {"name": "downtime_end", "type": "DateTime", "description": "The predicted end date and time of the downtime event."}, {"name": "downtime_minutes", "type": "Float", "description": "The predicted duration of the downtime event in minutes."}, {"name": "prediction_time", "type": "DateTime", "description": "Timestamp indicating when this prediction was generated."}, {"name": "model_version", "type": "String", "description": "Version identifier of the predictive model used to generate this prediction."}, {"name": "confidence_score", "type": "Float", "description": "A score indicating the model's confidence in this prediction (e.g., probability)."}, {"name": "reason_code", "type": "String", "description": "Predicted reason or category for the downtime."}], "relationships": [{"column": "node_id", "references_table": "node_meta", "references_column": "_id", "description": "Links the prediction to the specific node (well)."}, {"column": "operator_id", "references_table": "operator_meta", "references_column": "_id", "description": "Links the prediction to the responsible operator."}], "notes": "Schema synthesized based on description as raw schema was not provided."}, "equipment_store": {"description": "Stores information about equipment assets, linking them to nodes (physical location or association) and operators. Contains fields for detailed specifications (data), operational status, and external documentation links (URL).", "columns": [{"name": "_event_time", "type": "DateTime64(3)", "description": "System timestamp for record creation or update."}, {"name": "_id", "type": "String", "description": "Unique identifier for the equipment record."}, {"name": "data", "type": "String", "description": "Detailed information about the equipment, potentially stored as a JSON string or unstructured text (e.g., make, model, serial number, specifications)."}, {"name": "delete_flag", "type": "Int16", "description": "Flag indicating if the record is marked for deletion (soft delete). Non-zero usually means deleted."}, {"name": "nodeid", "type": "String", "description": "Identifier of the node where the equipment is installed or primarily associated. Links to node_meta._id."}, {"name": "operator", "type": "String", "description": "Identifier of the operator who owns or manages the equipment. Links to operator_meta._id."}, {"name": "url", "type": "Nullable(String)", "description": "A URL linking to external documentation, manuals, or information related to the equipment."}, {"name": "status", "type": "Nullable(String)", "description": "Current operational status of the equipment (e.g., 'Active', 'Inactive', 'Maintenance', 'Failed')."}], "relationships": [{"column": "nodeid", "references_table": "node_meta", "references_column": "_id", "description": "Links the equipment to its associated node (location/asset)."}, {"column": "operator", "references_table": "operator_meta", "references_column": "_id", "description": "Links the equipment to the responsible operator."}]}, "feature_store": {"description": "Stores calculated features or derived data points associated with nodes (e.g., wells, pads). These features are typically results from models, analytics, or aggregations and are used for monitoring, prediction, or further analysis.", "columns": [{"name": "_event_time", "type": "DateTime64(3)", "description": "System timestamp indicating when the feature record was ingested or calculated."}, {"name": "_id", "type": "String", "description": "Unique identifier for this specific feature value record."}, {"name": "node_name", "type": "String", "description": "The name of the node to which this feature pertains. Links to node_meta.name (or node_meta.attr.name)."}, {"name": "operator", "type": "String", "description": "Identifier of the operator associated with the node. Links to operator_meta._id."}, {"name": "source_type", "type": "String", "description": "Identifier for the source process, model, or calculation that generated this feature (e.g., 'Model_XYZ', 'DailyAggregation')."}, {"name": "time", "type": "DateTime64(3)", "description": "The timestamp relevant to the feature's value (e.g., the time the input data was from, or the time the calculation represents)."}, {"name": "value_type", "type": "String", "description": "The name or identifier of the feature itself (e.g., 'predicted_pressure_trend', '24hr_avg_flowrate', 'failure_risk_score')."}, {"name": "version", "type": "Nullable(String)", "description": "Version identifier for the source_type (e.g., model version, calculation script version)."}, {"name": "created_at", "type": "Nullable(DateTime64(3))", "description": "Timestamp indicating when this feature record was created in the feature store."}, {"name": "data", "type": "JSON", "description": "The actual calculated feature value(s). Stored as JSON to accommodate various data types (single value, array, object)."}], "relationships": [{"column": "node_name", "references_table": "node_meta", "references_column": "name", "description": "Links the feature to the node it describes, using the node name."}, {"column": "operator", "references_table": "operator_meta", "references_column": "_id", "description": "Links the feature record to the relevant operator."}]}, "node_meta": {"description": "Stores metadata for hierarchical items (nodes) within the operational structure, such as Wells, Pads, Areas, Teams, etc. Defines the structure and attributes of these entities.", "columns": [{"name": "_event_time", "type": "DateTime64(3)", "description": "System timestamp for record creation or update."}, {"name": "_id", "type": "String", "description": "Unique identifier for the node. This is the primary key referenced by many other tables."}, {"name": "attr", "type": "JSON", "description": "A JSON object containing various attributes specific to the node type (e.g., location, depth, configuration settings)."}, {"name": "enabled", "type": "UInt8", "description": "Flag indicating if the node is currently active or enabled (1 = enabled, 0 = disabled)."}, {"name": "operator_id", "type": "String", "description": "Identifier of the operator this node belongs to. Links to operator_meta._id."}, {"name": "parent_ids", "type": "<PERSON><PERSON><PERSON>(String)", "description": "An array containing the _id(s) of the parent node(s) in the hierarchy. Links to node_meta._id (self-referential)."}, {"name": "vector", "type": "Array(Nullable(Float32))", "description": "Numerical vector representation (embedding) of the node's metadata, used for similarity searches."}, {"name": "vector_text", "type": "Nullable(String)", "description": "The specific text content (concatenated metadata) used to generate the vector."}, {"name": "disable_deferment_target", "type": "Nullable(Bool)", "description": "Flag to explicitly exclude this node from being considered as a target in deferment calculations."}, {"name": "name", "type": "String", "description": "The primary name of the node (e.g., 'Well A-101', 'North Pad', 'Area 5'). Extracted from attr.name for easier access."}, {"name": "type", "type": "String", "description": "The type of the node (e.g., 'Well', 'Pad', 'Facility', 'Area', 'Team'). Extracted from attr.type for easier access."}, {"name": "updated_at", "type": "Nullable(DateTime64(3))", "description": "Timestamp indicating the last time the node's metadata was updated."}, {"name": "attr_is_bulk_test", "type": "Nullable(UInt8)", "description": "Flag extracted from attributes, potentially indicating if the node is involved in bulk testing procedures."}, {"name": "attr_config_last_actions_config_id", "type": "Nullable(String)", "description": "Identifier extracted from attributes, referencing a specific configuration related to last actions performed on the node."}], "relationships": [{"column": "operator_id", "references_table": "operator_meta", "references_column": "_id", "description": "Links the node to its owning/managing operator."}, {"column": "parent_ids", "references_table": "node_meta", "references_column": "_id", "description": "Defines the hierarchical structure by linking a node to its parent(s)."}]}, "operator_meta": {"description": "Stores metadata about operators (e.g., companies). Provides unique identifiers and names for operators, used to link data across various tables.", "columns": [{"name": "_event_time", "type": "DateTime64(3)", "description": "System timestamp for record creation or update."}, {"name": "id", "type": "Int16", "description": "A numeric identifier for the operator. May be a legacy ID or used for specific internal purposes."}, {"name": "name", "type": "String", "description": "The name of the operator company."}, {"name": "_id", "type": "String", "description": "Unique string identifier for the operator. This is the primary key referenced by other tables (as operator_id or operator)."}], "relationships": []}, "signals_clean": {"description": "Stores cleaned time-series sensor data (signals) from equipment and wells. Contains numerous columns representing specific measurements (e.g., pressure, temperature, flow rate) recorded at specific timestamps for each node.", "columns": [{"name": "_event_time", "type": "DateTime64(3)", "description": "System timestamp indicating when the signal data was ingested or processed."}, {"name": "_id", "type": "String", "description": "Unique identifier for this specific time-series data record/batch."}, {"name": "node_id", "type": "String", "description": "Identifier of the node (e.g., sensor, well) from which the signals originate. Links to node_meta._id."}, {"name": "operator", "type": "String", "description": "Identifier of the operator associated with the node. Links to operator_meta._id."}, {"name": "time", "type": "DateTime", "description": "The actual timestamp when the sensor readings were taken."}, {"name": "delete_flag", "type": "UInt8", "description": "Flag indicating if the record is marked for deletion (soft delete). 0 = active, 1 = deleted."}, {"name": "Flowrate", "type": "Nullable(Float32)", "description": "Measured flow rate."}, {"name": "CP_LP", "type": "Nullable(Float32)", "description": "Differential pressure between Casing Pressure and Line Pressure."}, {"name": "CP_TP", "type": "Nullable(Float32)", "description": "Differential pressure between Casing Pressure and Tubing Pressure."}, {"name": "Casing_Pressure", "type": "Nullable(Float32)", "description": "Measured pressure within the well casing."}, {"name": "Differential_Pressure", "type": "Nullable(Float32)", "description": "Measured differential pressure across a device (e.g., orifice plate)."}, {"name": "Gas_Temperature", "type": "Nullable(Float32)", "description": "Measured temperature of the gas flow."}, {"name": "Gas_Today", "type": "Nullable(Float32)", "description": "Cumulative gas volume measured today."}, {"name": "Oil_Flowrate", "type": "Nullable(Float32)", "description": "Measured oil flow rate."}, {"name": "Oil_Temperature", "type": "Nullable(Float32)", "description": "Measured temperature of the oil flow."}, {"name": "Oil_Today", "type": "Nullable(Float32)", "description": "Cumulative oil volume measured today."}, {"name": "Separator_Pressure", "type": "Nullable(Float32)", "description": "Measured pressure inside a separator vessel."}, {"name": "Separator_Temperature", "type": "Nullable(Float32)", "description": "Measured temperature inside a separator vessel."}, {"name": "Static_Pressure", "type": "Nullable(Float32)", "description": "Measured static pressure in the line."}, {"name": "Tubing_Pressure", "type": "Nullable(Float32)", "description": "Measured pressure within the well tubing."}, {"name": "Water_Flowrate", "type": "Nullable(Float32)", "description": "Measured water flow rate."}, {"name": "Water_Today", "type": "Nullable(Float32)", "description": "Cumulative water volume measured today."}, {"name": "gas_rate", "type": "Nullable(Float32)", "description": "Calculated or measured gas production rate."}, {"name": "Temperature", "type": "Nullable(Float32)", "description": "General temperature reading."}, {"name": "...", "type": "Nullable(Float32)", "description": "Numerous other specific sensor readings related to pressures, temperatures, flow rates, injection, plunger lift parameters, valve positions, cycle counts, etc. Each column name typically describes the specific measurement."}], "relationships": [{"column": "node_id", "references_table": "node_meta", "references_column": "_id", "description": "Links the signal data to the specific node it originates from."}, {"column": "operator", "references_table": "operator_meta", "references_column": "_id", "description": "Links the signal data to the responsible operator."}]}, "workflow_metadatatable": {"description": "Stores metadata for operational workflows, linking them to specific nodes (e.g., wells needing attention) and operators. Tracks workflow status, assignments, and associated tasks.", "columns": [{"name": "workflow_id", "type": "String", "description": "Unique identifier for this specific workflow instance."}, {"name": "NodeID", "type": "String", "description": "Identifier of the node (e.g., well, equipment) to which this workflow applies. Links to node_meta._id."}, {"name": "operator_id", "type": "String", "description": "Identifier of the operator associated with the node/workflow. Links to operator_meta._id."}, {"name": "workflow_template_id", "type": "String", "description": "Identifier for the type or template of this workflow (e.g., 'WellIntervention', 'RoutineCheck')."}, {"name": "Assignee", "type": "String", "description": "The user, role, or group currently assigned to work on this workflow or its active task."}, {"name": "Level", "type": "String", "description": "Priority, severity, or level classification of the workflow (e.g., 'High', 'Medium', 'Low')."}, {"name": "Status", "type": "String", "description": "Current status of the workflow (e.g., 'New', 'InProgress', 'PendingApproval', 'Completed', 'Snoozed', 'Cancelled')."}, {"name": "Snoozed", "type": "Boolean or DateTime", "description": "Indicates if the workflow is currently snoozed (temporarily paused), possibly with an expiry time."}, {"name": "ActiveTodolist", "type": "JSON or Array(String)", "description": "Stores details (or IDs) of the currently active tasks within this workflow instance."}, {"name": "CompletedTodolist", "type": "JSON or Array(String)", "description": "Stores details (or IDs) of the tasks that have been completed within this workflow instance."}, {"name": "created_at", "type": "DateTime", "description": "Timestamp indicating when the workflow instance was created."}, {"name": "updated_at", "type": "DateTime", "description": "Timestamp indicating the last time the workflow metadata was updated."}], "relationships": [{"column": "NodeID", "references_table": "node_meta", "references_column": "_id", "description": "Links the workflow to the specific node it pertains to."}, {"column": "operator_id", "references_table": "operator_meta", "references_column": "_id", "description": "Links the workflow to the responsible operator."}]}}