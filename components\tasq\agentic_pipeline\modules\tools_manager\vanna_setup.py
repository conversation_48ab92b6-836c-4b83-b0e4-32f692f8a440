import re

import pandas as pd
from openai import OpenAI
from vanna.openai import OpenAI_Chat
from vanna.vannadb import VannaDB_VectorStore

from components.tasq.agentic_pipeline.core.config import settings
from components.tasq.agentic_pipeline.modules.tools_manager.tiny_bird_setup import data_fetcher


vanna_model_name = settings.VANNA_MODEL_NAME
api_key = settings.VANNA_API_KEY
open_router_key = settings.OPEN_ROUTER_KEY
vanna_llm_model = settings.VANNA_LLM_MODEL


class MyVanna(VannaDB_VectorStore, OpenAI_Chat):
    def __init__(self, config=None):
        VannaDB_VectorStore.__init__(self, vanna_model=vanna_model_name,
                                     vanna_api_key=api_key, config=config)

        openai_client = OpenAI(
            api_key=config.get("openrouter_api_key"),
            base_url="https://openrouter.ai/api/v1"
        )

        OpenAI_Chat.__init__(self, client=openai_client, config=config)


vanna_client = MyVanna(config={'openrouter_api_key': open_router_key,
                               'model': vanna_llm_model})


def run_sql_tinybird(sql: str, **kwargs) -> pd.DataFrame:
    """
    Executes a SQL query against the Tinybird API using the project's
    data_fetcher and returns a pandas DataFrame.
    """
    try:
        response = data_fetcher(sql)
        response.raise_for_status()

        response_json = response.json()

        if isinstance(response_json, dict) and 'data' in response_json:
            data_to_load = response_json['data']
        else:
            data_to_load = response_json

        if not isinstance(data_to_load, list):
            data_to_load = [data_to_load]

        return pd.DataFrame(data_to_load)

    except Exception as e:
        print(f"Error in run_sql_tinybird: {e}")
        # Return an empty DataFrame in case of any error
        return pd.DataFrame()


vanna_client.run_sql = run_sql_tinybird
vanna_client.run_sql_is_set = True


def extract_sql(text):
    match = re.search(r"```sql\s*(.*?)\s*```", text, re.DOTALL)
    if match:
        return match.group(1).strip()
    else:
        return text.strip()
