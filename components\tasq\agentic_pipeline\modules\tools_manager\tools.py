import os
import time
from typing import List

import matplotlib.pyplot as plt
import pandas as pd
import seaborn as sns  # noqa
from google.adk.tools import ToolContext
from matplotlib import rcParams

from components.tasq.agentic_pipeline.core.config import settings
from components.tasq.agentic_pipeline.database.base import BaseSessionManager
from components.tasq.agentic_pipeline.modules.tools_manager.rundown_shortcut_queries import (
    RundownShortcutPerAssignee,
    RundownShortcutPerWell,
)
from components.tasq.agentic_pipeline.modules.tools_manager.tiny_bird_setup import data_fetcher
from components.tasq.agentic_pipeline.modules.tools_manager.tools_manager_utils.aws_utils import (
    upload_file_to_s3,
)
from components.tasq.agentic_pipeline.modules.tools_manager.tools_manager_utils.utils import (
    call_recommendation_signal_search,
    call_similar,
    call_time_series_search,
    generate_and_upload_pdf,
    get_arrays,
    plot_bore_graph,
)
from components.tasq.agentic_pipeline.modules.tools_manager.vanna_setup import extract_sql, vanna_client

logger = settings.LOGGER
ENV = settings.EXECUTION_ENVIRONMENT


def load_memory(session_id: str, db_session: BaseSessionManager):
    try:
        returning_text = ""
        conversation = db_session.get_session(session_id)
        messages = conversation.get("messages", [])
        messages = messages[:10]
        last_message = messages[-1:]
        for msg in messages:
            text = msg.get("text", "")
            if msg.get("author") != "user":
                author = "Agent"
            else:
                author = "User"
            returning_text += f"{author} Responded: {text}\n\n"
        return {"last_message": last_message, "report": returning_text}
    except Exception as e:
        raise RuntimeError(str(e))


def sql_tool(query: str, allow_llm_to_see_data: bool = True):
    """
    Generates sql query based on the user question.
    Using Vanna.AI.

    Args:
        query (str): User question.
        allow_llm_to_see_data (bool): Whether to allow the LLM to see the data.

    Returns:
        dict: {
                "status": "success", "sql_query": str
            } or {
                "status": "error", "error_message": str
            }
    """
    try:
        logger.info(f"Raw User Question: {query}")
        query = query + " must not add intermediate_sql in the response."
        generated_sql_query = vanna_client.generate_sql(
            question=query, allow_llm_to_see_data=allow_llm_to_see_data
        )
        generated_sql_query = extract_sql(generated_sql_query)
        logger.info(f"Generated SQL Query: {generated_sql_query}")
        return {"status": "success", "sql_query": generated_sql_query}
    except Exception as e:
        return {"status": "error", "error_message": str(e)}


def sql_executor(sql_query: str):
    """
    Retrieves data from database based on the sql query.

    Args:
        sql_query (str): sequel query
    Returns:
        dict: {
                "status": "success", "report": str
            } or {
                "status": "error", "error_message": str
            }
    """
    try:
        logger.debug(sql_query)
        sql_query = sql_query.rstrip().rstrip(";") + " FORMAT JSON"
        logger.debug("\n\n")
        logger.debug(sql_query)
        response = data_fetcher(sql_query).json()
        if "data" in response.keys():
            if len(response["data"]) > 0:
                response = response["data"]
                logger.debug(f"Recieved Data: {response}")
            else:
                logger.debug("\n\n")
                logger.debug("Recieved An Empty data!")
        else:
            response = "An error occurred while executing sql query: " + response["error"]
            logger.error(response)
        return {"status": "success", "report": response}
    except Exception as e:
        return {"status": "error", "error_message": str(e)}


def signal_search_tool(well_id: str, start_time: str, end_time: str, operator: str):
    """
    Fetches data from third party url.

    Args:
        well_id (str): ids for wells
        start_time (str): start time duration
        end_time (str): end time duration
        operator (str): name of the operator
    Returns:
        dict: {
                "status": "success", "report": str
            } or {
                "status": "error", "error_message": str
            }
    """
    try:
        operator = operator.lower()
        logger.info(
            f"""Signal search params - Wells: {well_id}, Start: {start_time}, End: {end_time}, Operator: {operator}"""
        )

        # Step 1: Get TimeSeries data
        time_series_result = call_time_series_search(
            operator=operator, end_time=end_time, start_time=start_time, node_id=well_id
        )

        # Step 2: Get Similar results
        similar_result = call_similar(operator=operator, node_id=well_id)

        # Step 3: Get enhanced Recommendation using Similar context
        recommendation_result = call_recommendation_signal_search(
            operator=operator, well_id=well_id, similar_context=similar_result
        )

        logger.debug(f"Time series: {time_series_result}")
        logger.debug(f"Similar: {similar_result}")
        logger.debug(f"Enhanced Recommendations: {recommendation_result}")

        return {
            "status": "success",
            "report": {
                "Recommendation": recommendation_result,
                "TimeSeries": time_series_result,
                "Similar": similar_result,
            },
        }
    except Exception as e:
        return {"status": "error", "error_message": str(e)}


def detailed_data_tool(trigger_parameter: str, operator_name: str, tool_context: ToolContext):
    """
    Fetches detailed data for the well or assignee.

    Args:
        trigger_parameter(str): Name of the well or assignee
        operator_name(str): Name of the operator
    """
    report_filename = str(time.time()) + ".pdf"
    rundown_shortcut_well = RundownShortcutPerWell(trigger_parameter, operator_name)
    rundown_shortcut_assignee = RundownShortcutPerAssignee(trigger_parameter, operator_name)

    well_validation = rundown_shortcut_well.validate_well()
    assignee_validation = rundown_shortcut_assignee.validate_assignee()
    if not well_validation and not assignee_validation:
        return "Not data found for well or for assignee"

    returning_dict = {}
    if well_validation:
        well_id, operator_name = well_validation[0]["target_node_id"], well_validation[0]["operator_name"]
        deferment_well_data = rundown_shortcut_well.deferment_well_data(well_id)
        detailed_prod_op_status_data = rundown_shortcut_well.detailed_prod_operational_status(well_id)

        # If deferment well data has artificial lift type
        for data in deferment_well_data.get("data"):  # noqa
            if "artificial" in data["lift_type"].lower():
                lift_specific_metrics_data = rundown_shortcut_well.get_lift_specific_metrics(well_id)
                returning_dict["lift_specific_metrics_data"] = lift_specific_metrics_data

        well_event_status_data = rundown_shortcut_well.get_well_event_statuses(operator_name)
        last_known_well_test_data = rundown_shortcut_well.get_last_known_well_test(well_id)
        returning_dict["deferment_well_data"] = deferment_well_data
        returning_dict["detailed_production_and_operational_status_data"] = detailed_prod_op_status_data
        returning_dict["well_event_status_data"] = well_event_status_data
        returning_dict["last_known_well_test_data"] = last_known_well_test_data
        tubing_pressure_data = rundown_shortcut_well.get_tubing_pressure(well_id)["data"]
        tubing_pressure = 1020.8
        if tubing_pressure_data:
            tubing_pressure = tubing_pressure_data[0]["Tubing_Pressure"]
        data_arrays = rundown_shortcut_well.get_data_arrays(trigger_parameter)["data"][0]
        if data_arrays:
            md_array = data_arrays["md_array"]
            inc_array = data_arrays["inc_array"]
            azimuth_array = data_arrays["azi_array"]
            x_values, y_values, _ = get_arrays(md_array, inc_array, azimuth_array)
            plot_filename = plot_bore_graph(x_values, y_values, tubing_pressure)
            url = upload_file_to_s3(plot_filename, plot_filename, file_type="png")
            tool_context.state["plot_url"] = url
            if os.path.exists(plot_filename):
                os.remove(plot_filename)

        pdf_cdn_link = generate_and_upload_pdf(returning_dict, report_filename)
        logger.info(f"This is the whole data from the queries:\n {returning_dict}")
        return {"well_detailed_report": returning_dict, "report_pdf_file": pdf_cdn_link}
    else:
        returning_dict["operator_name"] = rundown_shortcut_assignee.operator_name
        returning_dict["current_open_task"] = rundown_shortcut_assignee.get_current_open_tasks()
        returning_dict["recently_completed_tasks"] = rundown_shortcut_assignee.get_recently_completed_tasks()
        returning_dict["recent_comments"] = rundown_shortcut_assignee.get_recent_comments()
        return {"assignee_detailed_report": returning_dict}


def exit_loop(tool_context: ToolContext):
    logger.info("Loop Termination Called")
    tool_context.actions.escalate = True
    return {}


def plot_data(
    x_axis: List[int], y_axis: List[int], chart_type: str, x_name: str, y_name: str, tool_context: ToolContext
):
    """
    Generates a chart of the specified type using the given x and y data.

    Parameters:
    - x_axis (list): X-axis values (can be categorical, numerical, or temporal)
    - y_axis (list): Y-axis values (typically numerical)
    - chart_type (str): One of ['line', 'area', 'bar', 'box', 'scatter']
    - x_name (str): Label for x-axis (Column name of the x_axis values)
    - y_name (str): Label for y-axis (Column name of the y_axis values)

    Output:
    - File Url (str): File uploaded to s3 url.
    """
    try:
        rcParams["font.family"] = "sans-serif"
        rcParams["axes.labelsize"] = 12
        rcParams["axes.labelweight"] = "regular"
        rcParams["axes.titlesize"] = 18
        rcParams["axes.titleweight"] = "semibold"
        rcParams["xtick.color"] = "#EAEAEA"
        rcParams["ytick.color"] = "#EAEAEA"

        df = pd.DataFrame({x_name: x_axis, y_name: y_axis})

        number_of_points = len(x_axis)
        fig_width = min(max(10, 0.5 * number_of_points), 30)
        plt.figure(figsize=(fig_width, 6), facecolor="#0D2136")

        ax = plt.gca()  # noqa
        ax.set_facecolor("#0D2136")
        ax.figure.set_facecolor("#0D2136")
        ax.tick_params(colors="#EAEAEA")
        ax.spines["bottom"].set_color("#B7B8B9")
        ax.spines["top"].set_color("#B7B8B9")
        ax.spines["right"].set_color("#B7B8B9")
        ax.spines["left"].set_color("#B7B8B9")
        ax.grid(True, color="#B7B8B9", linestyle="--", alpha=0.7)  # noqa

        custom_palette = ["#F55D8B", "#E64E4E", "#E3E658", "#6EC08D", "#0077F0"]

        title = ""

        if chart_type == "line":
            sns.lineplot(data=df, x=x_name, y=y_name, marker="o", linewidth=2.5, color=custom_palette[0])
            title = "Line Chart"

        elif chart_type == "area":
            ax.fill_between(df[x_name], df[y_name], color=custom_palette[0], alpha=0.4)
            ax.plot(df[x_name], df[y_name], color=custom_palette[1], marker="o", linewidth=2)
            title = "Area Chart"

        elif chart_type in ["bar", "column"]:
            sns.barplot(data=df, x=x_name, y=y_name, color=custom_palette[2])
            title = "Bar Chart"

        elif chart_type == "box":
            sns.boxplot(data=df, x=x_name, y=y_name, palette=[custom_palette[3]])
            title = "Box Plot"

        elif chart_type == "scatter":
            sns.scatterplot(data=df, x=x_name, y=y_name, color=custom_palette[4], s=100, edgecolor="white")
            title = "Scatter Plot"

        else:
            raise ValueError("Unsupported chart type. Choose from: 'line', 'area', 'bar', 'box', 'scatter'.")

        plt.title(title, color="white", pad=20)
        # Abbreviate x_name if it exceeds 30 characters for x-label
        abbreviated_x_name = x_name[:15] + "..." if len(x_name) > 15 else x_name
        plt.xlabel(abbreviated_x_name, color="white")
        plt.ylabel(y_name, color="white")

        plt.tight_layout()
        plot_filename = "plot.png"
        plt.savefig(plot_filename, dpi=500, facecolor=plt.gcf().get_facecolor())  # noqa
        url = upload_file_to_s3(plot_filename, plot_filename, file_type="png")
        tool_context.state["plot_url"] = url

    finally:
        os.remove(plot_filename)
