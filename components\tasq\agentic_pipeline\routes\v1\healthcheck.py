from fastapi import APIRouter, status
from pydantic import BaseModel

router = APIRouter(
    tags=["HealthCheck"],
    responses={
        401: {"description": "Unauthorized"},
        403: {"description": "Forbidden"},
        404: {"description": "Not found"},
        408: {"description": "Request timeout"},
        500: {"description": "Internal server error"},
    },
)


class BaseResponse(BaseModel):
    status: int


class MessageResponse(BaseResponse):
    message: str


@router.get("/health-check")
def health_check():
    return MessageResponse(status=status.HTTP_200_OK, message="Connection to the server was successfull")
