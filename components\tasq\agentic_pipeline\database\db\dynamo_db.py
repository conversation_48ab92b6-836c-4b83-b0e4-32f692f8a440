import uuid
from datetime import datetime
from typing import Any, Dict, List, Optional
from urllib.parse import parse_qs, urlparse

from components.tasq.agentic_pipeline.core.config import settings
from components.tasq.agentic_pipeline.database.base import BaseSessionManager, SessionManagerFactory

try:
    import boto3
    from botocore.exceptions import ClientError
except ImportError:
    raise ImportError("Please install boto3")
from dataclasses import dataclass


@dataclass
class ReturnValueConfig:
    return_values: str = "UPDATED_NEW"


class DynamoSessionManager(BaseSessionManager):
    """
    Session manager implementation using DynamoDB for storage.
    """

    def __init__(
        self,
        dynamodb_client,
        sessions_table: str = "sessions",
        messages_table: str = "messages",
        users_table: str = "users",
        region_name: str = "us-east-1",
    ):
        if not boto3:
            raise ImportError("boto3 is required for DynamoSessionManager")

        self.dynamodb = dynamodb_client
        self.sessions_table = sessions_table
        self.messages_table = messages_table
        self.users_table = users_table
        self.region_name = region_name
        self.return_config = ReturnValueConfig()

    @classmethod
    def from_uri(cls, uri: str) -> "DynamoSessionManager":
        """
        Create a DynamoSessionManager from a URI.

        URI format:
        dynamodb://[region]/[profile]?sessions_table=sessions&messages_table=messages&users_table=users
        or
        dynamodb://region?sessions_table=sessions&messages_table=messages&users_table=users&aws_access_key_id=XXX&aws_secret_access_key=YYY
        """
        if not boto3:
            raise ImportError("boto3 is required for DynamoSessionManager")

        parsed = urlparse(uri)

        params = parse_qs(parsed.query)
        sessions_table = params.get("sessions_table", ["sessions"])[0]
        messages_table = params.get("messages_table", ["messages"])[0]
        users_table = params.get("users_table", ["users"])[0]

        region_name = settings.AWS_REGION

        aws_access_key_id = settings.AWS_ACCESS_KEY
        aws_secret_access_key = settings.AWS_SECRET_KEY

        if aws_access_key_id and aws_secret_access_key:
            dynamodb = boto3.resource(
                "dynamodb",
                region_name=region_name,
                aws_access_key_id=aws_access_key_id,
                aws_secret_access_key=aws_secret_access_key,
            )
        else:
            dynamodb = boto3.resource("dynamodb", region_name=region_name)

        return cls(
            dynamodb_client=dynamodb,
            sessions_table=sessions_table,
            messages_table=messages_table,
            users_table=users_table,
            region_name=region_name,
        )

    def _serialize_datetime(self, datetime_object):
        if isinstance(datetime_object, datetime):
            return datetime_object.isoformat()
        return datetime_object

    def _deserialize_datetime(self, datetime_object):
        """Convert ISO format strings back to datetime objects."""
        if isinstance(datetime_object, str):
            try:
                return datetime.fromisoformat(datetime_object)
            except (ValueError, AttributeError):
                return datetime_object
        return datetime_object

    def _prepare_item_for_storage(self, items_to_store: Dict[str, Any]) -> Dict[str, Any]:
        prepared = {}
        for key, item_to_store in items_to_store.items():
            if isinstance(item_to_store, datetime):
                prepared[key] = item_to_store.isoformat()
            elif isinstance(item_to_store, list):
                prepared[key] = [self._serialize_datetime(v) for v in item_to_store]
            elif isinstance(item_to_store, dict):
                prepared[key] = {k: self._serialize_datetime(v) for k, v in item_to_store.items()}
            else:
                prepared[key] = item_to_store
        return prepared

    def _prepare_item_from_storage(self, items_to_store: Dict[str, Any]) -> Dict[str, Any]:
        if not items_to_store:
            return items_to_store

        prepared = {}
        for key, item_to_store in items_to_store.items():
            if key in ["created_at", "updated_at"] and isinstance(item_to_store, str):
                try:
                    prepared[key] = datetime.fromisoformat(item_to_store)
                except (ValueError, AttributeError):
                    prepared[key] = item_to_store
            else:
                prepared[key] = item_to_store
        return prepared

    def create_user(self, user_id: str, user_data: Dict[str, Any] = None) -> None:
        record = {"user_id": user_id, **(user_data or {})}
        record.setdefault("created_at", datetime.utcnow())
        record["updated_at"] = datetime.utcnow()

        # Prepare item for storage
        item_to_store = self._prepare_item_for_storage(record)

        try:
            table = self.dynamodb.Table(self.users_table)
            table.put_item(Item=item_to_store)
        except ClientError as e:
            raise RuntimeError(f"Failed to create user: {e}")

    def get_user(self, user_id: str) -> Optional[Dict[str, Any]]:
        try:
            table = self.dynamodb.Table(self.users_table)
            response = table.get_item(Key={"user_id": user_id})

            if "Item" not in response:
                return None

            return self._prepare_item_from_storage(response["Item"])
        except ClientError as e:
            raise RuntimeError(f"Failed to get user: {e}")

    def create_message(self, author: str, text: str) -> str:
        message_id = str(uuid.uuid4())
        msg = {
            "message_id": message_id,
            "author": author,
            "text": text,
            "created_at": datetime.utcnow(),
            "updated_at": datetime.utcnow(),
        }

        item_for_storage = self._prepare_item_for_storage(msg)

        try:
            table = self.dynamodb.Table(self.messages_table)
            table.put_item(Item=item_for_storage)
            return message_id
        except ClientError as e:
            raise RuntimeError(f"Failed to create message: {e}")

    def get_messages(self, ids: List[str]) -> List[Dict[str, Any]]:
        if not ids:
            return []

        try:
            self.dynamodb.Table(self.messages_table)
            request_items = {self.messages_table: {"Keys": [{"message_id": mid} for mid in ids]}}

            response = self.dynamodb.batch_get_item(RequestItems=request_items)
            messages = response.get("Responses", {}).get(self.messages_table, [])

            # Prepare items from storage and maintain order
            resulted_items = []
            message_dict = {msg["message_id"]: self._prepare_item_from_storage(msg) for msg in messages}

            for mid in ids:
                if mid in message_dict:
                    resulted_items.append(message_dict[mid])

            return resulted_items
        except ClientError as e:
            raise RuntimeError(f"Failed to get messages: {e}")

    def create_session(
        self,
        session_id: str,
        user_id: str,
        app_name: str,
        operator_id: str,
        user_data: Dict[str, Any] = None,
    ) -> None:
        self.create_user(user_id, user_data)

        record = {
            "session_id": session_id,
            "user_id": user_id,
            "app_name": app_name,
            "message_ids": [],
            "created_at": datetime.utcnow(),
            "updated_at": datetime.utcnow(),
            "operator_id": operator_id,
        }

        item_for_storage = self._prepare_item_for_storage(record)

        try:
            table = self.dynamodb.Table(self.sessions_table)
            table.put_item(Item=item_for_storage)
        except ClientError as e:
            raise RuntimeError(f"Failed to create session: {e}")

    def get_session(self, session_id: str) -> Optional[Dict[str, Any]]:
        try:
            table = self.dynamodb.Table(self.sessions_table)
            response = table.get_item(Key={"session_id": session_id})

            if "Item" not in response:
                return None

            rec = self._prepare_item_from_storage(response["Item"])

            user = self.get_user(rec["user_id"])
            messages = self.get_messages(rec.get("message_ids", []))

            return {
                "session_id": rec["session_id"],
                "app_name": rec["app_name"],
                "created_at": rec.get("created_at"),
                "updated_at": rec.get("updated_at"),
                "user": user,
                "messages": messages,
                "operator_id": rec.get("operator_id"),
            }
        except ClientError as e:
            raise RuntimeError(f"Failed to get session: {e}")

    def update_session(
        self,
        session_id: str,
        messages: List[Dict[str, str]],
    ) -> bool:
        self._validate_messages(messages)
        base = self.get_session(session_id)

        if not base:
            return False

        new_ids = [self.create_message(m["author"], m["text"]) for m in messages]
        all_ids = [m["message_id"] for m in base["messages"]] + new_ids

        try:
            table = self.dynamodb.Table(self.sessions_table)
            table.update_item(
                Key={"session_id": session_id},
                UpdateExpression="""SET message_ids
= :ids, updated_at = :updated""",
                ExpressionAttributeValues={":ids": all_ids, ":updated": datetime.utcnow().isoformat()},
                ReturnValues=self.return_config.return_values,
            )
            return True
        except ClientError:
            return False

    def delete_session(self, session_id: str) -> bool:
        try:
            table = self.dynamodb.Table(self.sessions_table)
            self.return_config.return_values = "ALL_OLD"
            response = table.delete_item(
                Key={"session_id": session_id}, ReturnValues=self.return_config.return_values
            )
            return "Attributes" in response
        except ClientError:
            return False

    def session_exists(self, session_id: str) -> bool:
        try:
            table = self.dynamodb.Table(self.sessions_table)
            response = table.get_item(Key={"session_id": session_id}, ProjectionExpression="session_id")
            return "Item" in response
        except ClientError:
            return False

    def get_user_id(self, session_id: str) -> Optional[str]:
        rec = self.get_session(session_id)
        return rec.get("user", {}).get("user_id") if rec else None


SessionManagerFactory.register("dynamodb", DynamoSessionManager)
