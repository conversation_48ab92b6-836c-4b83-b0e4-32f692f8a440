import os
import shelve
import uuid
from datetime import datetime
from typing import Any, Dict, List, Optional
from urllib.parse import parse_qs, urlparse

from components.tasq.agentic_pipeline.database.base import BaseSessionManager, SessionManagerFactory


class ShelveSessionManager(BaseSessionManager):
    """
    Session manager implementation using Shelve for storage.
    """

    def __init__(
        self,
        shelf_session_file: str = "sessions.db",
        shelf_message_file: str = "messages.db",
        shelf_user_file: str = "users.db",
        base_dir: str = "",
    ):
        # Prepare file paths
        self.shelf_sessions = os.path.join(base_dir, shelf_session_file)
        self.shelf_messages = os.path.join(base_dir, shelf_message_file)
        self.shelf_users = os.path.join(base_dir, shelf_user_file)

        # Ensure directories exist
        for file_path in (self.shelf_sessions, self.shelf_messages, self.shelf_users):
            directory = os.path.dirname(file_path)
            if directory:
                os.makedirs(directory, exist_ok=True)

    @classmethod
    def from_uri(cls, uri: str) -> "ShelveSessionManager":
        """
        Create a ShelveSessionManager from a URI.

        URI format:
        shelve:///path/to/db/directory?session_file=sessions.db&message_file=messages.db&user_file=users.db

        If no query parameters are provided, default filenames will be used.
        """
        parsed = urlparse(uri)

        # Extract base directory from path
        base_dir = parsed.path
        if base_dir.startswith("/"):
            base_dir = base_dir[1:]  # Remove leading slash

        # If base_dir is empty or just "/", use current directory
        if not base_dir:
            base_dir = "."

        # Extract file names from query parameters
        params = parse_qs(parsed.query)

        return cls(
            shelf_session_file=params.get("session_file", ["sessions.db"])[0],
            shelf_message_file=params.get("message_file", ["messages.db"])[0],
            shelf_user_file=params.get("user_file", ["users.db"])[0],
            base_dir=base_dir,
        )

    def create_user(self, user_id: str, user_data: Dict[str, Any] = None) -> None:
        now = datetime.utcnow()
        record = {"user_id": user_id, **(user_data or {})}
        record.setdefault("created_at", now)
        record["updated_at"] = now

        with shelve.open(self.shelf_users) as shelf:
            shelf[user_id] = record

    def get_user(self, user_id: str) -> Optional[Dict[str, Any]]:
        with shelve.open(self.shelf_users) as shelf:
            return shelf.get(user_id)

    def create_message(self, author: str, text: str) -> str:
        now = datetime.utcnow()
        msg = {"author": author, "text": text, "created_at": now, "updated_at": now}
        mid = str(uuid.uuid4())

        with shelve.open(self.shelf_messages) as shelf:
            shelf[mid] = msg

        return mid

    def get_messages(self, ids: List[str]) -> List[Dict[str, Any]]:
        out = []
        with shelve.open(self.shelf_messages) as shelf:
            for mid in ids:
                message = shelf.get(mid)
                if message:
                    out.append({"message_id": mid, **message})
        return out

    def create_session(
        self,
        session_id: str,
        user_id: str,
        app_name: str,
        operator_id: str,
        user_data: Dict[str, Any] = None,
    ) -> None:
        self.create_user(user_id, user_data)

        now = datetime.utcnow()
        record = {
            "session_id": session_id,
            "user_id": user_id,
            "app_name": app_name,
            "message_ids": [],
            "created_at": now,
            "updated_at": now,
            "operator_id": operator_id,
        }

        with shelve.open(self.shelf_sessions) as shelf:
            shelf[session_id] = record

    def get_session(self, session_id: str) -> Optional[Dict[str, Any]]:
        with shelve.open(self.shelf_sessions) as shelf:
            rec = shelf.get(session_id)

        if not rec:
            return None

        user = self.get_user(rec["user_id"])
        messages = self.get_messages(rec.get("message_ids", []))

        return {
            "session_id": rec["session_id"],
            "app_name": rec["app_name"],
            "created_at": rec.get("created_at"),
            "updated_at": rec.get("updated_at"),
            "user": user,
            "messages": messages,
            "operator_id": rec.get("operator_id"),
        }

    def update_session(
        self,
        session_id: str,
        messages: List[Dict[str, str]],
    ) -> bool:
        self._validate_messages(messages)
        base = self.get_session(session_id)

        if not base:
            return False

        new_ids = [self.create_message(m["author"], m["text"]) for m in messages]
        all_ids = [m["message_id"] for m in base["messages"]] + new_ids
        now = datetime.utcnow()

        with shelve.open(self.shelf_sessions) as shelf:
            rec = shelf[session_id]
            rec["message_ids"] = all_ids
            rec["updated_at"] = now
            shelf[session_id] = rec

        return True

    def delete_session(self, session_id: str) -> bool:
        with shelve.open(self.shelf_sessions) as shelf:
            if session_id in shelf:
                del shelf[session_id]
                return True
        return False

    def session_exists(self, session_id: str) -> bool:
        with shelve.open(self.shelf_sessions) as shelf:
            return session_id in shelf

    def get_user_id(self, session_id: str) -> Optional[str]:
        rec = self.get_session(session_id)
        return rec.get("user", {}).get("user_id") if rec else None


SessionManagerFactory.register("shelve", ShelveSessionManager)
