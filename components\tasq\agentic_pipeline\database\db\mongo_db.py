from datetime import datetime
from typing import Any, Dict, List, Optional
from urllib.parse import parse_qs, urlparse

from bson.objectid import ObjectId

from components.tasq.agentic_pipeline.database.base import BaseSessionManager, SessionManagerFactory

try:
    from pymongo import MongoClient
except ImportError:
    raise ImportError("Please install pymongo")


class MongoSessionManager(BaseSessionManager):
    """
    Session manager implementation using MongoDB for storage.
    """

    def __init__(
        self,
        client: "MongoClient",
        db_name: str,
        sessions_collection: str = "sessions",
        messages_collection: str = "messages",
        users_collection: str = "users",
    ):
        if not MongoClient:
            raise ImportError("pymongo is required for MongoSessionManager")

        self.client = client
        self.db = client[db_name]
        self.mongo_sessions = self.db[sessions_collection]
        self.mongo_messages = self.db[messages_collection]
        self.mongo_users = self.db[users_collection]

    @classmethod
    def from_uri(cls, uri: str) -> "MongoSessionManager":
        """
        Create a MongoSessionManager from a URI.

        URI format:
        mongodb://[username:password@]host[:port]/database[?collection_prefix=prefix]
        """
        if not MongoClient:
            raise ImportError("pymongo is required for MongoSessionManager")

        parsed = urlparse(uri)

        # Extract parameters
        params = parse_qs(parsed.query)
        collection_prefix = params.get("collection_prefix", [""])[0]

        # Extract database name from path
        db_name = parsed.path.strip("/")
        if not db_name:
            raise ValueError("Database name not specified in MongoDB URI")

        # Remove query string to create proper MongoDB URI
        mongodb_uri = uri.split("?")[0]

        # Create MongoDB client
        client = MongoClient(mongodb_uri)

        # Create and return manager
        return cls(
            client=client,
            db_name=db_name,
            sessions_collection=f"{collection_prefix}sessions",
            messages_collection=f"{collection_prefix}messages",
            users_collection=f"{collection_prefix}users",
        )

    def create_user(self, user_id: str, user_data: Dict[str, Any] = None) -> None:
        now = datetime.utcnow()
        record = {"user_id": user_id, **(user_data or {})}
        record.setdefault("created_at", now)
        record["updated_at"] = now

        # use user_id as Mongo _id for easy reference
        self.mongo_users.replace_one({"_id": user_id}, {**record, "_id": user_id}, upsert=True)

    def get_user(self, user_id: str) -> Optional[Dict[str, Any]]:
        doc = self.mongo_users.find_one({"_id": user_id}, {"_id": 0})
        return doc

    def create_message(self, author: str, text: str) -> str:
        now = datetime.utcnow()
        msg = {"author": author, "text": text, "created_at": now, "updated_at": now}
        res = self.mongo_messages.insert_one(msg)
        return str(res.inserted_id)

    def get_messages(self, ids: List[str]) -> List[Dict[str, Any]]:
        out = []
        object_ids = [ObjectId(mid) for mid in ids]

        for doc in self.mongo_messages.find({"_id": {"$in": object_ids}}):
            mid = str(doc.pop("_id"))
            out.append({"message_id": mid, **doc})

        return out

    def create_session(
        self,
        session_id: str,
        user_id: str,
        app_name: str,
        operator_id: str,
        user_data: Dict[str, Any] = None,
    ) -> None:
        # self._validate_messages(messages)
        self.create_user(user_id, user_data)

        now = datetime.utcnow()
        record = {
            "session_id": session_id,
            "user_id": user_id,
            "app_name": app_name,
            "message_ids": [],
            "created_at": now,
            "updated_at": now,
            "operator_id": operator_id,
        }

        self.mongo_sessions.replace_one({"session_id": session_id}, record, upsert=True)

    def get_session(self, session_id: str) -> Optional[Dict[str, Any]]:
        rec = self.mongo_sessions.find_one({"session_id": session_id}, {"_id": 0})

        if not rec:
            return None

        user = self.get_user(rec["user_id"])
        messages = self.get_messages(rec.get("message_ids", []))

        return {
            "session_id": rec["session_id"],
            "app_name": rec["app_name"],
            "created_at": rec.get("created_at"),
            "updated_at": rec.get("updated_at"),
            "user": user,
            "messages": messages,
            "operator_id": rec.get("operator_id"),
        }

    def update_session(
        self,
        session_id: str,
        messages: List[Dict[str, str]],
    ) -> bool:
        self._validate_messages(messages)
        base = self.get_session(session_id)

        if not base:
            return False

        new_ids = [self.create_message(m["author"], m["text"]) for m in messages]
        all_ids = [m["message_id"] for m in base["messages"]] + new_ids
        now = datetime.utcnow()

        res = self.mongo_sessions.update_one(
            {"session_id": session_id}, {"$set": {"message_ids": all_ids, "updated_at": now}}
        )

        return res.matched_count > 0

    def delete_session(self, session_id: str) -> bool:
        res = self.mongo_sessions.delete_one({"session_id": session_id})
        return res.deleted_count == 1

    def session_exists(self, session_id: str) -> bool:
        return self.mongo_sessions.count_documents({"session_id": session_id}, limit=1) == 1

    def get_user_id(self, session_id: str) -> Optional[str]:
        rec = self.get_session(session_id)
        return rec.get("user", {}).get("user_id") if rec else None


SessionManagerFactory.register("mongodb", MongoSessionManager)
