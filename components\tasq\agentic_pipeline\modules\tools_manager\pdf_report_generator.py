import json
import os
import warnings

from matplotlib.backends.backend_pdf import PdfPages

from components.tasq.agentic_pipeline.core.config import settings
from components.tasq.agentic_pipeline.modules.tools_manager.tools_manager_utils.pdf_report_gen_utils import (
    WellProductionReportGenerator,
)

warnings.filterwarnings("ignore")

logger = settings.LOGGER
report_pdf_gen = WellProductionReportGenerator()


def generate_well_report_pdf(json_data, output_filename="well_production_report.pdf"):
    report_pdf_gen.setup_matplotlib_style()

    try:
        os.makedirs("Reports", exist_ok=True)
        output_filename = os.path.join("Reports", output_filename)
        if isinstance(json_data, str):
            converted_data = json.loads(json_data)
        else:
            converted_data = json_data

        deferment_data = converted_data.get("deferment_well_data", {}).get("data", [])
        if deferment_data and len(deferment_data) > 0:
            well_name = deferment_data[0].get("well_name")

        with PdfPages(output_filename) as pdf_pages:
            report_pdf_gen.create_title_page(pdf_pages, well_name)

            report_pdf_gen.create_production_overview(pdf_pages, deferment_data)

            event_data = converted_data.get("well_event_status_data")
            report_pdf_gen.create_operational_status_page(pdf_pages, event_data)

            logger.info("Compiling executive summary...")
            report_pdf_gen.create_summary_page(pdf_pages, converted_data)

        logger.info(f"Professional PDF report generated successfully: {output_filename}")
        logger.info(f"Report contains visualizations for {len(deferment_data)} data points")

    except Exception as e:
        logger.info(f"Error generating PDF: {str(e)}")
        return False

    return output_filename
