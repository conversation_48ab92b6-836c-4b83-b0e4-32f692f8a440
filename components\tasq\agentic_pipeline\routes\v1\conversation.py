import json
import os
import time
import uuid

from fastapi import APIRouter, Depends, HTTPException, status
from fastapi.responses import JSONResponse, StreamingResponse
from google.adk.sessions import InMemorySessionService
from google.genai import types
from pydantic import BaseModel

from components.tasq.agentic_pipeline.core.config import settings
from components.tasq.agentic_pipeline.core.dependencies import get_db_instance
from components.tasq.agentic_pipeline.core.security import Context, get_server_api_key
from components.tasq.agentic_pipeline.core.utils import (
    get_all_operators,
    get_operator_name,
    load_formatted_the_schema,
    validate_operator_id,
    event_generator
)
from components.tasq.agentic_pipeline.database.base import BaseSessionManager
from components.tasq.agentic_pipeline.modules.agents_manager.agents_manager_utils.utils import ask_root, ask_root_sql
from components.tasq.agentic_pipeline.modules.agents_manager.root_agent import get_root_agent, get_sql_agent, get_sql_query_root_optimized, get_simplified_sql_query_root
from components.tasq.agentic_pipeline.modules.tools_manager.tools import load_memory

logger = settings.LOGGER
router = APIRouter(
    tags=["Conversations"],
    responses={
        401: {"description": "Unauthorized"},
        403: {"description": "Forbidden"},
        404: {"description": "Not found"},
        408: {"description": "Request timeout"},
        500: {"description": "Internal server error"},
    },)

os.environ["OPENROUTER_API_KEY"] = settings.OPEN_ROUTER_KEY
os.environ["OPENROUTER_API_BASE"] = settings.OPEN_ROUTER_BASE


class SqlDataRequest(BaseModel):
    user_question: str
    operator_id: str


class ConversationRequest(BaseModel):
    user_question: str
    session_id: str


class InitializeSessionRequest(BaseModel):
    user_id: str
    operator_id: str


class BaseResponse(BaseModel):
    status: int


class SqlQueryResponse(BaseResponse):
    query: str


class MessageResponse(BaseResponse):
    message: str


class InitializeSessionResponse(BaseResponse):
    session_id: str


class ConversationResponse(BaseResponse):
    chat: str
    plot_url: str = ""


class SqlDataResponse(BaseResponse):
    chat: str


class MessageHistoryResponse(BaseResponse):
    session_info: dict
    messages: list


@router.post("/initialize-session")
def initiate_session(
        request_data: InitializeSessionRequest,
        db: BaseSessionManager = Depends(get_db_instance),
        context: Context = Depends(get_server_api_key)):

    session_id = str(uuid.uuid4())
    user_id = request_data.user_id
    operator_id = request_data.operator_id
    if len(user_id) < 1:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="User id should not be empty"
        )
    validate_operator_id(operator_id)
    try:
        db.create_session(
            session_id=session_id,
            user_id=user_id,
            app_name=settings.APP,
            operator_id=operator_id
        )
        logger.info(f"Session created successfully for user: {str(user_id)}")
        return InitializeSessionResponse(status=status.HTTP_200_OK,
                                         session_id=session_id)
    except Exception as e:
        logger.error(f"An error occurred while initiating session: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail='Something went wrong')


@router.post("/get_sql_query")
async def get_sql_query(input_data: SqlDataRequest,
                        context: Context = Depends(get_server_api_key)):
    user_question = input_data.user_question

    temp_app_name = str(uuid.uuid4())
    temp_user_id = str(uuid.uuid4())
    temp_session_id = str(uuid.uuid4())
    operator_id = input_data.operator_id
    if not operator_id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Invalid session: missing operator_id"
        )
    operator_name = get_operator_name(operator_id)
    if not operator_name:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Please provide valid operator id")
    all_operators = get_all_operators()
    schema = load_formatted_the_schema()

    temp_service = None
    try:
        temp_service = InMemorySessionService()
        await temp_service.create_session(
            app_name=temp_app_name,
            user_id=temp_user_id,
            session_id=temp_session_id,
            state={'validated_result': "",
                   "generated_sql_query": "",
                   "database_schema": schema,
                   "operator_name": operator_name,
                   "all_operators": all_operators,
                   "updated_user_question": ""}
        )
    except Exception:
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                            detail="Error occured while creating adk session")
    try:
        runner, run_config = get_simplified_sql_query_root(temp_service,
                                                          temp_app_name)
        response = ask_root_sql(user_question,
                                temp_session_id,
                                temp_user_id,
                                runner,
                                run_config)
    except Exception as e:
        logger.error(f"An error occured while interacting with root agent, Details: {str(e)}")
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                            detail="An error occured while interacting with root agent")
    finally:
        if temp_service:
            try:
                await temp_service.delete_session(
                    app_name=temp_app_name,
                    user_id=temp_user_id,
                    session_id=temp_session_id
                )
            except Exception as e:
                logger.error(f"Error deleting adk session: {str(e)}")

    return SqlQueryResponse(status=status.HTTP_200_OK,
                            query=response)


@router.post("/get_sql_data")
async def process_sql_request(
    user_input_data: SqlDataRequest,
    context: Context = Depends(get_server_api_key)
):
    user_question = user_input_data.user_question

    temp_app_name = str(uuid.uuid4())
    temp_user_id = str(uuid.uuid4())
    temp_session_id = str(uuid.uuid4())
    operator_id = user_input_data.operator_id
    if not operator_id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Invalid session: missing operator_id"
        )
    operator_name = get_operator_name(operator_id)
    if not operator_name:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Please provide valid operator id")
    all_operators = get_all_operators()
    schema = load_formatted_the_schema()

    temp_service = None
    try:
        temp_service = InMemorySessionService()
        await temp_service.create_session(
            app_name=temp_app_name,
            user_id=temp_user_id,
            session_id=temp_session_id,
            state={'validated_result': "",
                   "generated_sql_query": "",
                   "database_schema": schema,
                   "operator_name": operator_name,
                   "all_operators": all_operators,
                   "updated_user_question": "",
                   "plot_url": ""}
        )
    except Exception:
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                            detail="Error occured while creating adk session")
    try:
        runner = get_sql_agent(temp_service, temp_app_name)
        response = ask_root(user_question,
                            temp_session_id,
                            temp_user_id,
                            runner)
    except Exception as e:
        logger.error(f"An error occured while interacting with root agent, Details: {str(e)}")
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                            detail="An error occured while interacting with root agent")
    finally:
        if temp_service:
            try:
                await temp_service.delete_session(
                    app_name=temp_app_name,
                    user_id=temp_user_id,
                    session_id=temp_session_id
                )
            except Exception as e:
                logger.error(f"Error deleting adk session: {str(e)}")

    return SqlDataResponse(status=status.HTTP_200_OK,
                           chat=response)


@router.post("/process-message")
async def process_message(
    user_input_data: ConversationRequest,
    db: BaseSessionManager = Depends(get_db_instance),
    context: Context = Depends(get_server_api_key)
):
    user_question = user_input_data.user_question
    if len(user_question) < 1:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="User Question should not be empty"
        )
    session_id = user_input_data.session_id
    if not db.session_exists(session_id):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Please provide a valid session_id"
        )

    session = db.get_session(session_id)

    user_id = session.get('user').get('user_id')
    operator_id = session.get('operator_id')
    if not operator_id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Invalid session: missing operator_id"
        )
    operator_name = get_operator_name(operator_id)
    if not session:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Please initiate session")
    all_operators = get_all_operators()
    schema = load_formatted_the_schema()
    conversation = load_memory(session_id, db).get('report')
    last_conversation = load_memory(session_id, db).get('last_message')
    try:
        temp_service = InMemorySessionService()
        await temp_service.create_session(
            app_name=settings.APP,
            user_id=user_id,
            session_id=session_id,
            state={'current_conversation': conversation,
                   'last_conversation': last_conversation,
                   'validated_result': "",
                   "generated_sql_query": "",
                   "database_schema": schema,
                   "operator_name": operator_name,
                   "all_operators": all_operators,
                   "plot_url": ""}
        )
    except Exception:
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                            detail="Error occured while creating adk session")
    try:
        runner = get_root_agent(temp_service)
        response = ask_root(user_question,
                            session_id,
                            user_id,
                            runner)
        messages = [{'author': 'user', 'text': user_question},
                    {'author': 'agent', 'text': response}]
    except Exception as e:
        logger.error(
        f"An error occured while interacting with root agent, Details: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="An error occured while interacting with root agent")
    try:
        db.update_session(session_id, messages)
    except Exception as e:
        logger.error(f"""An error occured while while updating the database,
                     Details: {str(e)}""")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="An error occured while updating the database")
    session_data = await temp_service.get_session(
        app_name=settings.APP, user_id=user_id, session_id=session_id)
    session_data = session_data.state
    plot_url = session_data.get('plot_url')
    try:
        temp_service.delete_session(app_name=settings.APP,
                                    user_id=user_id,
                                    session_id=session_id)
    except Exception as e:
        logger.error(f"An error occured while deleting adk session: {str(e)}")
    logger.info("Completed conversation successfully!")
    return ConversationResponse(status=status.HTTP_200_OK,
                                chat=response,
                                plot_url=plot_url)


@router.post("/streaming")
async def process_message_stream(
    user_input_data: ConversationRequest,
    db: BaseSessionManager = Depends(get_db_instance),
    context: Context = Depends(get_server_api_key)
):
    user_question = user_input_data.user_question
    if len(user_question) < 1:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="User Question should not be empty"
        )

    session_id = user_input_data.session_id
    if not db.session_exists(session_id):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Please provide a valid session_id"
        )

    session = db.get_session(session_id)

    user_id = session.get('user').get('user_id')
    operator_id = session.get('operator_id')
    if not operator_id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Invalid session: missing operator_id"
        )

    operator_name = get_operator_name(operator_id)
    if not session:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Please initiate session"
        )

    all_operators = get_all_operators()
    schema = load_formatted_the_schema()
    conversation = load_memory(session_id, db).get('report')
    last_conversation = load_memory(session_id, db).get('last_message')

    temp_service = None
    try:
        temp_service = InMemorySessionService()
        await temp_service.create_session(
            app_name=settings.APP,
            user_id=user_id,
            session_id=session_id,
            state={
                'current_conversation': conversation,
                'last_conversation': last_conversation,
                'validated_result': "",
                "generated_sql_query": "",
                "database_schema": schema,
                "operator_name": operator_name,
                "all_operators": all_operators,
                "plot_url": ""
            }
        )
    except Exception:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error occurred while creating adk session"
        )

    try:
        runner = get_root_agent(temp_service)
        session_obj = await temp_service.get_session(
            app_name=settings.APP, user_id=user_id, session_id=session_id
        )

        return StreamingResponse(
            event_generator(user_id, session_id, user_question, runner, temp_service, session_obj, settings.APP),
            media_type="text/plain",
            headers={
                "Cache-Control": "no-cache",
                "Connection": "keep-alive",
                "Content-Type": "text/event-stream",
                "Access-Control-Allow-Origin": "*",
                "Access-Control-Allow-Headers": "Cache-Control"
            }
        )

    except Exception as e:
        logger.error(f"An error occurred while interacting with root agent, Details: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="An error occurred while interacting with root agent"
        )


@router.post("/get_sql_data_stream")
async def process_sql_request_stream(
    user_input_data: SqlDataRequest,
    context: Context = Depends(get_server_api_key)
):
    user_question = user_input_data.user_question
    if len(user_question) < 1:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="User Question should not be empty"
        )

    operator_id = user_input_data.operator_id
    if not operator_id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Invalid session: missing operator_id"
        )

    operator_name = get_operator_name(operator_id)
    if not operator_name:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Please provide valid operator id"
        )

    temp_app_name = str(uuid.uuid4())
    temp_user_id = str(uuid.uuid4())
    temp_session_id = str(uuid.uuid4())

    all_operators = get_all_operators()
    schema = load_formatted_the_schema()

    temp_service = None
    try:
        temp_service = InMemorySessionService()
        await temp_service.create_session(
            app_name=temp_app_name,
            user_id=temp_user_id,
            session_id=temp_session_id,
            state={
                'validated_result': "",
                "updated_user_question": "",
                'generated_sql_query': "",
                'database_schema': schema,
                'operator_name': operator_name,
                'all_operators': all_operators,
                'plot_url': ""
            }
        )

        runner = get_sql_agent(temp_service, temp_app_name)
        session_obj = await temp_service.get_session(
            app_name=temp_app_name,
            user_id=temp_user_id,
            session_id=temp_session_id
        )

        return StreamingResponse(
            event_generator(
                temp_user_id,
                temp_session_id,
                user_question,
                runner,
                temp_service,
                session_obj,
                temp_app_name
            ),
            media_type="text/plain",
            headers={
                "Cache-Control": "no-cache",
                "Connection": "keep-alive",
                "Content-Type": "text/event-stream",
                "Access-Control-Allow-Origin": "*",
                "Access-Control-Allow-Headers": "Cache-Control"
            }
        )

    except Exception as e:
        if temp_service:
            try:
                await temp_service.delete_session(
                    app_name=temp_app_name,
                    user_id=temp_user_id,
                    session_id=temp_session_id
                )
            except Exception as cleanup_error:
                logger.error(f"Error cleaning up session after setup failure: {str(cleanup_error)}")

        logger.error(f"An error occurred while interacting with SQL agent, Details: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="An error occurred while interacting with SQL agent"
        )


@router.post("/events")
async def get_conversation_events(
    user_input_data: ConversationRequest,
    db: BaseSessionManager = Depends(get_db_instance),
    context: Context = Depends(get_server_api_key)
):
    """
    Process user message and return ALL agent messages as a single JSON array.

    This endpoint provides visibility into the complete agent
    interaction flow, showing each agent's processing steps, tool calls,
    and intermediate results before delivering the final response.

    Returns: A JSON array of agent message events.
    """
    user_question = user_input_data.user_question
    if len(user_question) < 1:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="User Question should not be empty"
        )
    session_id = user_input_data.session_id
    if not db.session_exists(session_id):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Please provide a valid session_id"
        )

    session = db.get_session(session_id)
    user_id = session.get('user').get('user_id')
    operator_id = session.get('operator_id')
    if not operator_id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Invalid session: missing operator_id"
        )
    operator_name = get_operator_name(operator_id)

    if not session:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Please initiate session")

    temp_service = None
    events_list = []
    final_response = ""

    try:
        all_operators = get_all_operators()
        schema = load_formatted_the_schema()
        conversation = load_memory(session_id, db).get('report')
        last_conversation = load_memory(session_id, db).get('last_message')

        temp_service = InMemorySessionService()
        temp_service.create_session(
            app_name=settings.APP,
            user_id=user_id,
            session_id=session_id,
            state={'current_conversation': conversation,
                   'last_conversation': last_conversation,
                   'validated_result': "",
                   "generated_sql_query": "",
                   "database_schema": schema,
                   "operator_name": operator_name,
                   "all_operators": all_operators}
        )

        runner = get_root_agent(temp_service)

        response_content = types.Content(
            role="user",
            parts=[types.Part(text=user_question)]
        )

        events = runner.run(
            user_id=user_id,
            session_id=session_id,
            new_message=response_content
        )

        messages = [{'author': 'user', 'text': user_question}]

        for event in events:
            message_content = event.content.parts[0].text if event.content and event.content.parts else None

            # If message is a JSON string, parse it into an object
            if isinstance(message_content, str) and message_content.strip().startswith(('{', '[')):
                try:
                    message_content = json.loads(message_content)
                except json.JSONDecodeError:
                    # Not a valid JSON, leave as is
                    pass

            event_data = {
                "timestamp": time.time(),
                "agent": getattr(event, 'author', 'unknown'),
                "message": message_content,
                "is_final": event.is_final_response(),
                "event_type": event.__class__.__name__
            }
            events_list.append(event_data)

            if event.is_final_response() and message_content:
                if isinstance(message_content, (dict, list)):
                    final_response = json.dumps(message_content)
                else:
                    final_response = message_content

        # Store conversation in database
        if final_response:
            messages.append({'author': 'agent', 'text': final_response})
            try:
                db.update_session(session_id, messages)
            except Exception as e:
                logger.error(f"Error updating database: {str(e)}")

    except Exception as e:
        logger.error(f"Error in streaming conversation: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"An error occurred during agent processing: {str(e)}"
        )
    finally:
        if temp_service:
            try:
                temp_service.delete_session(
                    app_name=settings.APP,
                    user_id=user_id,
                    session_id=session_id
                )
            except Exception as e:
                logger.error(f"Error deleting adk session: {str(e)}")

    return JSONResponse(content=events_list)


@router.get("/history/{session_id}")
def get_message_history(
    session_id: str,
    db: BaseSessionManager = Depends(get_db_instance),
    context: Context = Depends(get_server_api_key)
):
    """
    Retrieve the complete message history for a given session.

    Returns all messages exchanged between the user and agent
    in chronological order, along with session metadata.

    Args:
        session_id: The session ID to retrieve history for

    Returns:
        JSON object containing messages array and session info
    """
    # Validate session exists
    if not db.session_exists(session_id):
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Session not found"
        )

    try:
        # Get session data
        session = db.get_session(session_id)
        if not session:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Session data not found"
            )

        # Extract messages and session info
        messages = session.get('messages', [])
        user_info = session.get('user', {})
        operator_id = session.get('operator_id')

        # Get operator name if available
        operator_name = None
        if operator_id:
            try:
                operator_name = get_operator_name(operator_id)
            except Exception as e:
                logger.error(f"Error getting operator name: {str(e)}")

        session_info = {
            "session_id": session_id,
            "app_name": session.get('app_name'),
            "user_id": user_info.get('user_id'),
            "operator_id": operator_id,
            "operator_name": operator_name,
            "message_count": len(messages),
            "created_at": session.get('created_at'),
            "updated_at": session.get('updated_at'),
            "user": {
                "user_id": user_info.get('user_id'),
                "created_at": user_info.get('created_at'),
                "updated_at": user_info.get('updated_at')
            }
        }

        return MessageHistoryResponse(
            status=status.HTTP_200_OK,
            session_info=session_info,
            messages=messages
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error retrieving message history: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="An error occurred while retrieving message history"
        )
