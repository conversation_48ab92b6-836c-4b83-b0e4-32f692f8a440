from typing import Optional

import matplotlib.pyplot as plt
import numpy as np
import requests
from matplotlib import rcParams

from components.tasq.agentic_pipeline.core.config import settings
from components.tasq.agentic_pipeline.modules.agents_manager.config import HEADERS
from components.tasq.agentic_pipeline.modules.tools_manager.pdf_report_generator import (
    generate_well_report_pdf,
)
from components.tasq.agentic_pipeline.modules.tools_manager.tiny_bird_setup import data_fetcher
from components.tasq.agentic_pipeline.modules.tools_manager.tools_manager_utils.aws_utils import (
    upload_file_to_s3,
)

logger = settings.LOGGER


def get_complete_vector_data(response_json, descriptions: list):
    complete_data_json = {}
    for element in response_json:
        data_json_per_node = {descriptions[0]: {}, descriptions[1]: {}}
        response_matches = element["matches"][0]
        event_ids_list = response_matches["ids"]
        for index, signal_type_event_id_list in enumerate(event_ids_list):
            for signal_type_event_id in signal_type_event_id_list:
                vectors_query = (
                    f"SELECT * from timeseries_vector_v3 where _id = '{signal_type_event_id}' FORMAT JSON"
                )
                vectors_data = data_fetcher(vectors_query)
                if vectors_data.status_code == 200:
                    vectors_data_list = vectors_data.json().get("data")
                    for vectors_data_json in vectors_data_list:
                        vectors_signal_type = vectors_data_json.get("signal_type", "")
                        data_json_per_node[descriptions[index]]["WellName"] = vectors_data_json.get(
                            "nodeid", ""
                        )
                        data_json_per_node[descriptions[index]]["Operator"] = vectors_data_json.get(
                            "operator", ""
                        )
                        data_json_per_node[descriptions[index]]["SignalType"] = vectors_signal_type
        complete_data_json[element.get("nodeid")] = data_json_per_node
    return complete_data_json


def summarize_similar_insights(similar_result):
    """Extract key insights from Similar API response for Recommendation context"""
    if not similar_result or not isinstance(similar_result, dict):
        return ""

    # Extract key information from similar cases
    key_insights = []

    # Process similar cases data
    if "documents" in similar_result:
        for doc in similar_result["documents"][:3]:  # Top 3 most relevant
            if "body" in doc and "description" in doc:
                insight = f"Similar case: {doc.get('description', '')} - {doc.get('body', '')[:200]}"
                key_insights.append(insight)

    return "; ".join(key_insights) if key_insights else ""


def call_recommendation_signal_search(operator: str, well_id: str, similar_context: Optional[str] = None):
    """
    Fetching recommendations for specific well.

    Args:
        operator (str): Operator name
        well_id (str): Well name
    """
    try:
        url = f"https://{settings.RECOMMENDATION_URL}"
        if similar_context:
            # Extract key insights from similar context for enhanced analysis
            context_summary = summarize_similar_insights(similar_context)
            enhanced_query = f"""Analyze well {well_id} deferment using similar case context: {context_summary}.

            Provide comprehensive analysis with:
            1) POSSIBLE ROOT CAUSES: Identify 3-5 potential technical reasons based on operational data patterns
            2) RECOMMENDED ANALYSIS: Suggest specific analytical methods to validate each cause
            3) SIGNAL SEARCH SUGGESTION: Recommend deeper investigation using signal search tool"""
        else:
            enhanced_query = f"""Analyze well {well_id} deferment situation.

            Provide comprehensive analysis with:
            1) POSSIBLE ROOT CAUSES: Identify 3-5 potential technical reasons based on operational data patterns
            2) RECOMMENDED ANALYSIS: Suggest specific analytical methods to validate each cause
            3) SIGNAL SEARCH SUGGESTION: Recommend deeper investigation using signal search tool"""

        json_body = {
            "query": enhanced_query,
            "stream": False,
            "useAll": False,
            "wellMetaData": {"operator_name": operator, "operator_id": 4, "nodeid": well_id, "level": "well"},
        }
        response = requests.post(url=url, headers=HEADERS[0], json=json_body)
        logger.debug(f"Recommendation data: {response.json()}")
        if response.status_code == 200:
            return response.json().get("recommendations", "")
    except Exception as e:
        logger.error(f"Error occurred while calling recommendation: {str(e)}")
        return f"Error occurred while calling recommendation: {str(e)}"


def auth_refresh_token():
    url = f"https://{settings.REFRESH_TOKEN_URL}"
    json_body = {"type": "refresh", "email": "<EMAIL>", "refreshToken": settings.REFRESH_TOKEN}
    refresh_token_response = requests.post(url=url, headers=HEADERS[3], json=json_body)
    if refresh_token_response.status_code == 200:
        return refresh_token_response.json().get("tokens").get("IdToken")
    else:
        logger.error(
            f"""Refresh Token returned {refresh_token_response.status_code} \n
                            with an error of {refresh_token_response.json()}"""
        )


def call_time_series_search(operator: str, end_time: str, start_time: str, node_id: str):
    try:
        url = f"https://{settings.TIME_SERIES_URL}"
        auth_token = auth_refresh_token()
        headers = HEADERS[1]
        descriptions = ["Casing Pressure", "Flowrate"]
        headers["authorization"] = auth_token
        json_body = {
            "operator_name": operator,
            "end_time": end_time,
            "start_time": start_time,
            "nodeid": node_id,
            "descriptions": descriptions,
            "event_tag": "test",
            "created_by": "",
            "sensitivity": 2,
        }
        time_series_response = requests.post(url=url, headers=headers, json=json_body)
        logger.debug(f"Timeseries before json: {time_series_response.json()}")
        if time_series_response.status_code == 200:
            response_json = time_series_response.json().get("response", "")
            if len(response_json) > 15:
                response_json = response_json[:10]
            complete_data_json = get_complete_vector_data(response_json, descriptions)
            return complete_data_json
        else:
            logger.error(
                f"""Time series search endpoint returned {time_series_response.status_code} \n
                            with an error of {time_series_response.json()}"""
            )
    except Exception as e:
        return f"Error occurred while calling time series search: {str(e)}"


def call_similar(operator: str, node_id: str):
    try:
        url = f"https://{settings.SIMILAR_URL}"
        auth_token = auth_refresh_token()
        headers = HEADERS[2]
        headers["authorization"] = auth_token
        json_body = {
            "query": "testing plunger",
            "stream": False,
            "useAll": False,
            "wellMetaData": {"operator_name": operator, "operator_id": 4, "nodeid": node_id, "level": "well"},
        }
        similar_response = requests.post(url=url, json=json_body, headers=headers)
        if similar_response.status_code == 200:
            return similar_response.json()
        else:
            logger.error(
                f"""Similar Endpoint returned {similar_response.status_code} \n
                            with an error of {similar_response.json()}"""
            )
    except Exception as e:
        logger.error(f"Exception occurred: {str(e)}")


def generate_and_upload_pdf(
    returning_dict,
    report_filename,
):
    local_pdf_path = f"{settings.PDF_REPORT_DIR}/{report_filename}"
    generate_well_report_pdf(returning_dict, report_filename)
    s3_key = f"{settings.PDF_REPORT_DIR}/{report_filename}"
    return upload_file_to_s3(local_pdf_path, s3_key, file_type="pdf")


def get_arrays(md_array, inc_array, azimuth_array):
    try:
        measured_depth = np.array(md_array, dtype=float)
        inc = np.radians(np.array(inc_array, dtype=float))
        azimuth = np.radians(np.array(azimuth_array, dtype=float))
    except Exception as e:
        print("Conversion to float failed:", e)
        raise

    x_values = [0]
    y_values = [0]
    z_values = [0]

    for index in range(1, len(measured_depth)):
        delta_md = measured_depth[index] - measured_depth[index - 1]
        avg_inc = (inc[index] + inc[index - 1]) / 2
        avg_azimuth = (azimuth[index] + azimuth[index - 1]) / 2

        delta_x = delta_md * np.sin(avg_inc) * np.sin(avg_azimuth)
        delta_y = delta_md * np.sin(avg_inc) * np.cos(avg_azimuth)
        delta_z = delta_md * np.cos(avg_inc)

        x_values.append(x_values[-1] + delta_x)
        y_values.append(y_values[-1] + delta_y)
        z_values.append(z_values[-1] + delta_z)

    return x_values, y_values, z_values


def plot_bore_graph(x_values, y_values, tubing_pressure):
    try:
        rcParams["font.family"] = "sans-serif"
        rcParams["axes.labelsize"] = 12
        rcParams["axes.labelweight"] = "regular"
        rcParams["axes.titlesize"] = 18
        rcParams["axes.titleweight"] = "semibold"
        rcParams["xtick.color"] = "#EAEAEA"
        rcParams["ytick.color"] = "#EAEAEA"

        plt.figure(figsize=(10, 8), facecolor="#0D2136")
        ax = plt.gca()  # noqa
        ax.set_facecolor("#0D2136")
        ax.figure.set_facecolor("#0D2136")
        ax.tick_params(colors="#EAEAEA")
        ax.spines["bottom"].set_color("#B7B8B9")
        ax.spines["top"].set_color("#B7B8B9")
        ax.spines["right"].set_color("#B7B8B9")
        ax.spines["left"].set_color("#B7B8B9")
        ax.grid(True, color="#B7B8B9", linestyle="--", alpha=0.7)

        path_color = "#0077F0"
        toe_color = "#F55D8B"
        arrow_color = "orange"

        plt.plot(x_values, y_values, color=path_color, label="Well Path (Plan View)", linewidth=2)
        plt.scatter(x_values[-1], y_values[-1], color=toe_color, zorder=5, label="Well Toe")

        plt.annotate(
            f"Tubing Pressure: {tubing_pressure} psi",
            xy=(x_values[-1], y_values[-1]),
            xytext=(x_values[-1] - 300, y_values[-1] + 50),
            arrowprops=dict(arrowstyle="->", color=arrow_color),
            fontsize=12,
            color="white",
            backgroundcolor="black",
        )

        plt.xlabel("X (meters)", color="white")
        plt.ylabel("Y (meters)", color="white")
        plt.title("Plan View of Wellbore with Tubing Pressure", color="white", pad=20)
        plt.axis("equal")
        plt.legend()
        plt.tight_layout()

        plot_filename = "plot.png"
        plt.savefig(plot_filename, dpi=500, facecolor=plt.gcf().get_facecolor())  # noqa
        return plot_filename

    finally:
        plt.close()
