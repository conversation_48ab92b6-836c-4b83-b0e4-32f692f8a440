from typing import Tuple

from components.tasq.agentic_pipeline.core.config import settings
from components.tasq.agentic_pipeline.modules.tools_manager.tiny_bird_setup import data_fetcher

logger = settings.LOGGER


def execute_query(query: str) -> dict:
    response = data_fetcher(query)
    if response.status_code != 200:
        return f"Query failed ({response.status_code}): {response.text}"
    return response.json()


class RundownShortcutPerWell:
    """
    A utility to fetch various well-related data from Tinybird
    """

    def __init__(self, well_name: str, operator_name: str):
        self.well_name = well_name
        self.operator_name = operator_name

    def validate_well(self) -> str:
        sql = f"""SELECT
            n._id AS target_node_id,
            o.name AS operator_name,
            n.name AS well_name
        FROM node_meta AS n
        JOIN operator_meta AS o ON n.operator_id = o._id
        WHERE
            LOWER(toString(o.name)) = LOWER('{self.operator_name}')
            AND LOWER(toString(n.name)) = LOWER('{self.well_name}')
            AND LOWER(n.type) = 'well'
            AND n.enabled = TRUE
        LIMIT 1
        FORMAT JSON"""
        validated_data = execute_query(sql)
        records = validated_data.get("data", [])
        return records

    def deferment_well_data(self, well_id: str) -> dict:
        sql = f"""
        WITH latest_date AS (
            SELECT node_id, MAX(date) AS point_time
            FROM deferment_mv
            WHERE node_id = '{well_id}'
            AND deferment_type IN ('gas_rate','boe_rate','oil_rate')
            GROUP BY node_id
        )
        SELECT
            n.name AS well_name,
            d.date      AS point_time,
            d.production,
            d.deferment,
            d.deferment_type,
            CASE
            WHEN d.production>0 AND d.deferment=0 THEN 'Flowing'
            WHEN d.production>0 AND d.deferment>0 THEN 'Partially Deferring'
            WHEN d.production=0 AND d.deferment>0 THEN 'Down / 100% Deferring'
            ELSE 'Status Unknown'
            END AS well_status,
            n.attr.lift_type AS lift_type
        FROM deferment_mv AS d
        JOIN latest_date AS ld ON d.node_id=ld.node_id AND d.date=ld.point_time
        JOIN node_meta AS n FINAL ON d.node_id=n._id
        ORDER BY d.deferment_type
        LIMIT 3 FORMAT JSON
    """
        return execute_query(sql)

    def detailed_prod_operational_status(self, well_id: str) -> dict:
        sql = f"""
            SELECT
              s.time               AS point_time,
              s.Casing_Pressure,
              s.Tubing_Pressure,
              s.Separator_Pressure,
              s.Static_Pressure,
              s.Flowrate,
              s.Oil_Flowrate,
              s.Gas_Today,
              s.Oil_Today,
              s.Separator_Temperature
            FROM signals_clean AS s
            WHERE s.node_id = '{well_id}'
              AND s.time >= now() - INTERVAL 24 HOUR
            ORDER BY s.time DESC
            LIMIT 1 FORMAT JSON
        """
        return execute_query(sql)

    def get_lift_specific_metrics(self, well_id: str) -> Tuple[dict, dict, dict]:
        queries = {
            "downtime": (
                """
                SELECT
                  summary AS downtime_summary,
                  body    AS downtime_details,
                  point_time      AS downtime_start_time,
                  created_by      AS downtime_created_by
                FROM categorized_comments FINAL
                WHERE nodeid = '{wid}'
                  AND LOWER(toString(type)) = 'downtime'
                  AND end_date IS NULL
                ORDER BY point_time DESC
                LIMIT 1 FORMAT JSON
                """,
            ),
            "waiting_on": (
                """
                SELECT
                  body               AS waiting_on_details,
                  point_time         AS waiting_on_start_time,
                  created_by         AS waiting_on_created_by
                FROM categorized_comments FINAL
                WHERE nodeid = '{wid}'
                  AND LOWER(toString(type)) = 'waiting on'
                  AND end_date IS NULL
                ORDER BY point_time DESC
                LIMIT 1 FORMAT JSON
                """,
            ),
            "alarms": (
                """
                SELECT
                  time        AS alarm_time,
                  value       AS alarm_value
                FROM alarms_data FINAL
                WHERE nodeid = '{wid}'
                  AND time >= now() - INTERVAL 48 HOUR
                ORDER BY time DESC
                LIMIT 3 FORMAT JSON
                """,
            ),
        }
        data_base_result = {}
        for key, template in queries.items():
            sql = template.format(wid=well_id)  # noqa
            data_base_result[key] = execute_query(sql)
        return data_base_result["downtime"], data_base_result["waiting_on"], data_base_result["alarms"]

    def get_well_event_statuses(self, operator_name: str) -> Tuple[dict, dict]:
        templates = [
            """
            SELECT
              WorkflowName AS task_name,
              Assignee     AS current_assignee,
              DateCreated  AS task_created_date,
              dateDiff('day',DateCreated,now()) AS days_open
            FROM workflow_output_table FINAL
            WHERE NodeID = '{wn}'
              AND DateClosed IS NULL
              AND LOWER(toString(Operator)) = '{op}'
            ORDER BY days_open DESC
            LIMIT 3 FORMAT JSON
            """,
            """
            SELECT
              work_order_number,
              time             AS reported_time
            FROM response_store FINAL
            WHERE node_name    = '{wn}'
              AND LOWER(toString(operator)) = '{op}'
              AND LOWER(toString(value_type)) = 'manual tasq'
              AND work_order_number IS NOT NULL
            ORDER BY time DESC
            LIMIT 3 FORMAT JSON
            """,
        ]
        data_base_result = []
        for sql in templates:
            data_base_result.append(execute_query(sql.format(wn=self.well_name, op=operator_name)))  # noqa
        return tuple(data_base_result)

    def get_last_known_well_test(self, well_id: str) -> dict:
        sql = f"""
            SELECT
              time            AS last_test_time,
              Well_Test_Gas,
              Well_Test_Oil
            FROM signals_clean
            WHERE node_id = '{well_id}'
              AND (Well_Test_Gas IS NOT NULL OR Well_Test_Oil IS NOT NULL)
            ORDER BY time DESC
            LIMIT 1 FORMAT JSON
        """
        return execute_query(sql)

    def get_tubing_pressure(self, well_id: str):
        sql = f"""
        SELECT
            s.time AS last_test_time,
            s.Tubing_Pressure
        FROM signals_clean s
        WHERE
            s.node_id = '{well_id}'
            AND s.Tubing_Pressure IS NOT NULL
        ORDER BY s.time DESC
        LIMIT 1 FORMAT JSON
        """
        return execute_query(sql)

    def get_data_arrays(self, well_id: str):
        sql = f"""
SELECT
    JSONExtractArrayRaw(JSONExtractRaw(data, 'survey'), 'md')  AS md_array,
    JSONExtractArrayRaw(JSONExtractRaw(data, 'survey'), 'tvd') AS tvd_array,
    JSONExtractArrayRaw(JSONExtractRaw(data, 'survey'), 'inc') AS inc_array,
    JSONExtractArrayRaw(JSONExtractRaw(data, 'survey'), 'azi') AS azi_array
    FROM equipment_store
WHERE nodeid = '{well_id}'
    AND JSONExtractRaw(data, 'survey') IS NOT NULL
LIMIT 1 FORMAT JSON
        """
        return execute_query(sql)


class RundownShortcutPerAssignee:
    def __init__(self, person_identifier: str, operator_name: str):
        self.person_identifier = person_identifier
        self.operator_name = operator_name

    def validate_assignee(
        self,
    ):
        query = f"""-- Step 0: Validate Person's
        Existence as Assignee for Operator (Operator-Specific, Parameterized)
SELECT
    (SELECT LOWER('{self.person_identifier}')
    WHERE EXISTS (
SELECT 1
    FROM workflow_output_table t FINAL
    WHERE LOWER(toString(t.Operator)) = LOWER('{self.operator_name}')
    AND position(LOWER(toString(t.Assignee)), LOWER('{
        self.person_identifier}')) > 0
    )
) AS person FORMAT JSON"""
        return execute_query(query).get("data")[0].get("person")

    def get_current_open_tasks(
        self,
    ):
        print("Inside Current Open Task")
        query = f"""-- Query 1: Open Tasks/Workflows Assigned to Person for
        Operator (Parameterized)
SELECT
    t.WorkflowName AS tasq_name,
    t.NodeID AS related_well_or_node,
    n.type AS node_type,
    t.DateCreated AS assignment_date,
    dateDiff('day', t.DateCreated, now()) AS days_open,
    t.PredictionType AS task_type
FROM workflow_output_table t FINAL
LEFT JOIN node_meta n FINAL ON
n.name= t.NodeID AND LOWER(toString(n.type)) = 'well'WHERE
LOWER(toString(t.Operator)) = LOWER('{self.operator_name}')
    AND t.DateClosed IS NULL
    AND (
        (
LOWER(JSONExtractString(t.Assignee, 'initial_assignment')) = LOWER('{
    self.person_identifier}') AND
(NOT has(JSONExtractKeys(t.Assignee), 'reassignment') OR JSONExtractString(
t.Assignee, 'reassignment') = '')
        ) OR
        (
            has(JSONExtractKeys(t.Assignee), 'reassignment') AND
            LOWER(JSONExtractString(t.Assignee,
            'reassignment', 'new_assignee')) = LOWER('{
                self.person_identifier}')
        )
    )
    AND (n.enabled = True OR n._id IS NULL)
ORDER BY days_open DESC
LIMIT 10
FORMAT JSON"""
        return execute_query(query)

    def get_recently_completed_tasks(
        self,
    ):
        query = f"""
                SELECT
                    t.WorkflowName AS tasq_name,
                    t.NodeID AS related_well_or_node,
                    t.DateCreated AS assignment_date,
                    t.DateClosed AS completion_date,
                    t.PredictionType AS task_type
                FROM workflow_output_table t FINAL
                WHERE
                    LOWER(toString(t.Operator)) = LOWER('{self.operator_name}')
                    AND t.DateClosed IS NOT NULL
                    AND t.DateClosed >= now() - INTERVAL 7 DAY
                    AND (
                    (
                    LOWER(JSONExtractString(t.Assignee,
                    'initial_assignment')) = LOWER('{self.person_identifier}')
                    AND
                    (
                    NOT has(JSONExtractKeys(t.Assignee), 'reassignment') OR
                    JSONExtractString(t.Assignee, 'reassignment') = '{
                        self.person_identifier}' OR
                    LOWER(JSONExtractString(JSONExtractString(t.Assignee,
                    'reassignment'), 'reassigned_by')) = LOWER('{
                        self.person_identifier}')
                    )
                    ) OR
                    (
                    has(JSONExtractKeys(t.Assignee), 'reassignment') AND
                    LOWER(JSONExtractString(t.Assignee,
                    'reassignment', 'new_assignee')) = LOWER('{
                        self.person_identifier}')
                    )
                    )
                ORDER BY t.DateClosed DESC
                LIMIT 10
                FORMAT JSON"""
        return execute_query(query)

    def get_recent_comments(
        self,
    ):
        query = f"""
                -- Query 3: Recent Comments/Actions by Person for Operator (
            Parameterized, Last 7 days)WITH OperatorParam AS (
                SELECT _id FROM operator_meta WHERE LOWER(
                toString(name)) = LOWER('{self.operator_name}') LIMIT 1
            )
            SELECT
c.point_time,
c.nodeid AS related_node_id, -- This is node_meta._idn.name
AS related_node_name,
c.type AS comment_type,
c.summary AS comment_summary,
c.body AS comment_body,
c.category,
c.sub_category
            FROM categorized_comments c FINALCROSS JOIN OperatorParam op
            LEFT JOIN node_meta n FINAL ON c.nodeid = n._id
            WHERE
                c.operator = op._id -- Filter by the looked-up operator_id
                AND LOWER(toString(c.created_by)) = LOWER('{
                    self.person_identifier}')
                AND c.point_time >= now() - INTERVAL 7 DAYAND c.delete_flag = 0
                ORDER BY c.point_time DESC
            LIMIT 10
            FORMAT JSON"""
        return execute_query(query)

    def recent_involvement_in_task_reassign(
        self,
    ):
        pass
