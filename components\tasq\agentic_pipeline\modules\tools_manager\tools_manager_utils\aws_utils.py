import boto3
from botocore.exceptions import ClientError, NoCredentialsError

from components.tasq.agentic_pipeline.core.config import settings


def create_bucket_if_not_exists(bucket_name, aws_access_key, aws_secret_key, aws_region):
    s3 = boto3.client(
        "s3", region_name=aws_region, aws_access_key_id=aws_access_key, aws_secret_access_key=aws_secret_key
    )

    try:
        s3.head_bucket(Bucket=bucket_name)
        print(f"Bucket '{bucket_name}' already exists.")
    except ClientError as e:
        error_code = int(e.response["Error"]["Code"])
        if error_code == 404:
            print(f"Bucket '{bucket_name}' does not exist. Creating...")
            if aws_region == "us-east-1":
                s3.create_bucket(Bucket=bucket_name)
            else:
                s3.create_bucket(
                    Bucket=bucket_name, CreateBucketConfiguration={"LocationConstraint": aws_region}
                )
            print(f"Bucket '{bucket_name}' created.")
        else:
            raise e


def upload_file_to_s3(local_file_path, s3_key, file_type: str, expiry_seconds=3600):
    try:
        s3 = boto3.client(
            "s3",
            region_name=settings.AWS_REGION,
            aws_access_key_id=settings.AWS_ACCESS_KEY,
            aws_secret_access_key=settings.AWS_SECRET_KEY,
        )
        if file_type == "pdf":
            extra_args = {"ContentType": "application/pdf"}
        else:
            extra_args = {"ContentType": "image/png"}

        s3.upload_file(local_file_path, settings.S3_BUCKET_NAME, s3_key, extra_args)

        presigned_url = s3.generate_presigned_url(
            "get_object", Params={"Bucket": settings.S3_BUCKET_NAME, "Key": s3_key}, ExpiresIn=expiry_seconds
        )
        return presigned_url

    except FileNotFoundError:
        return "The file was not found."
    except NoCredentialsError:
        return "Invalid AWS credentials."
