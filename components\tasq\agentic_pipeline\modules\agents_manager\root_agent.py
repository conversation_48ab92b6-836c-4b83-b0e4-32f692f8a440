from typing import Optional
from google.adk.agents import SequentialAgent
from google.adk.runners import Runner, RunConfig

from components.tasq.agentic_pipeline.core.config import settings
from components.tasq.agentic_pipeline.modules.agents_manager.agents\
    import GetAgents

logger = settings.LOGGER
agents = GetAgents()


def create_optimized_run_config(enable_streaming: bool = False,
                                max_calls: int = 50) -> RunConfig:
    if enable_streaming:
        return RunConfig(
            streaming_mode="sse",
            support_cfc=True,
            max_llm_calls=max_calls,
            save_input_blobs_as_artifacts=False,
            response_modalities=["TEXT"]
        )
    else:
        return RunConfig(
            streaming_mode=None,
            max_llm_calls=max_calls,
            save_input_blobs_as_artifacts=False,
            response_modalities=["TEXT"]
        )


def get_root_agent(temp_service) -> Runner:
    memory_agent = agents.get_memory_agent()
    router_agent = agents.get_base_router_agent()
    root_agent = SequentialAgent(
        name="RootAgent",
        sub_agents=[memory_agent, router_agent],
    )

    runner = Runner(
        agent=root_agent,
        app_name=settings.APP,
        session_service=temp_service,
    )
    return runner


def get_sql_agent(temp_service, app_name: str) -> Runner:
    agent = agents.sql_dedicated_agent()

    runner = Runner(
        agent=agent,
        app_name=app_name,
        session_service=temp_service,
    )
    return runner


def get_sql_query_root(temp_service, app_name: str) -> Runner:
    agent = agents.sql_query_dedicated_agent()

    runner = Runner(
        agent=agent,
        app_name=app_name,
        session_service=temp_service,
    )
    return runner


def get_sql_query_root_optimized(temp_service, app_name: str,
                                 run_config: Optional[
                                     RunConfig] = None) -> Runner:
    agent = agents.sql_query_dedicated_agent()

    if run_config is None:
        run_config = create_optimized_run_config()

    runner = Runner(
        agent=agent,
        app_name=app_name,
        session_service=temp_service,
    )
    return runner, run_config

def get_simplified_sql_query_root(temp_service, app_name: str,
                                 run_config: Optional[
                                     RunConfig] = None) -> Runner:
    agent = agents.simplified_sql_query_dedicated_agent()

    if run_config is None:
        run_config = create_optimized_run_config()

    runner = Runner(
        agent=agent,
        app_name=app_name,
        session_service=temp_service,
    )
    return runner, run_config
