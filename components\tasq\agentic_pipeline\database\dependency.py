import asyncio
from typing import Dict, Optional
from urllib.parse import urlparse

from components.tasq.agentic_pipeline.database.base import BaseSessionManager, SessionManagerFactory


class DatabaseDependency:
    """
    Singleton class to manage database dependencies with lazy initialization.
    Supports multiple database instances with different configurations.
    """

    _instances: Dict[str, BaseSessionManager] = {}
    _initialized: bool = False
    _initialization_lock = asyncio.Lock()
    _default_instance_name: str = "shelve"

    @classmethod
    async def initialize(cls, uri: str) -> None:
        """
        Initialize database connections based on configuration.

        Args:
            config: Dictionary mapping instance names to database URIs
        """
        async with cls._initialization_lock:
            if cls._initialized:
                return

            parsed = urlparse(uri)
            scheme = parsed.scheme.lower()
            print(scheme)
            # Create session manager instances based on URIs
            cls._instances[scheme] = SessionManagerFactory.create_manager(uri)

            # Mark as initialized
            cls._initialized = True

    @classmethod
    async def get_instance(cls, name: Optional[str] = None) -> BaseSessionManager:
        """
        Get a specific database instance by name.

        Args:
            name: Instance name (or None for default)

        Returns:
            Database instance
        """
        instance_name = name or cls._default_instance_name

        if not cls._initialized:
            raise RuntimeError(
                """Database dependencies not initialized.
                Call initialize() first."""
            )

        if instance_name not in cls._instances:
            raise KeyError(f"No database instance named '{instance_name}' was configured")

        return cls._instances[instance_name]

    @classmethod
    def get_instance_sync(cls, name: Optional[str] = None) -> BaseSessionManager:
        """
        Synchronous version of get_instance for non-async contexts.
        Should only be used after initialization.
        """
        instance_name = name or cls._default_instance_name

        if not cls._initialized:
            raise RuntimeError(
                """Database dependencies not initialized.
                Call initialize() first."""
            )

        if instance_name not in cls._instances:
            raise KeyError(f"No database instance named '{instance_name}' was configured")

        return cls._instances[instance_name]
