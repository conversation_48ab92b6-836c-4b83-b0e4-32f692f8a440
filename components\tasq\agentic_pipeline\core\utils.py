import json
from dataclasses import dataclass, field
from typing import Any, Dict, List
import uuid

from fastapi import HTT<PERSON>Ex<PERSON>, status
from google.adk.agents.callback_context import CallbackContext
from typing import AsyncGenerator
from google.adk.agents.invocation_context import InvocationContext
from google.genai import types
import asyncio

from components.tasq.agentic_pipeline.core.config import settings
from components.tasq.agentic_pipeline.modules.tools_manager.tiny_bird_setup import data_fetcher


@dataclass
class Column:
    name: str
    type: str  # noqa
    description: str


@dataclass
class Relationship:
    column: str
    references_table: str
    references_column: str
    description: str


@dataclass
class Table:
    name: str
    description: str
    columns: List[Column] = field(default_factory=list)
    relationships: List[Relationship] = field(default_factory=list)


def load_schema_from_file() -> Dict[str, Table]:
    with open(settings.SCHEMA_FILE, "r", encoding="utf-8") as f:
        raw = json.load(f)

    tables: Dict[str, Table] = {}
    for table_name, table_data in raw.items():
        table = Table(name=table_name, description=table_data.get("description", ""))
        for col in table_data.get("columns", []):
            table.columns.append(
                Column(name=col["name"], type=col["type"], description=col.get("description", ""))
            )
        for rel in table_data.get("relationships", []):
            table.relationships.append(
                Relationship(
                    column=rel["column"],
                    references_table=rel["references_table"],
                    references_column=rel["references_column"],
                    description=rel.get("description", ""),
                )
            )
        tables[table_name] = table
    return tables


def load_formatted_the_schema():
    returning_text = ""
    schema = load_schema_from_file()
    for name, table in schema.items():
        returning_text += (
            f"Table: {name}\n   Table {name}'s Description: {table.description}\n   Table {name}'s Columns:"
        )
        for col in table.columns:
            returning_text += f"    - {col.name} ({col.type}): {col.description}"
        if table.relationships:
            returning_text += f"\n  Table {name}'s Relationships:"
            for rel in table.relationships:
                returning_text += (
                    f"    - {rel.column} -> {rel.references_table}.{rel.references_column}: {rel.description}"
                )
    return returning_text


def get_operator_name(operator_id: str):
    operator_name_fetching_query = (
        f"""SELECT name AS operator_name FROM operator_meta WHERE _id = '{operator_id}' LIMIT 1 FORMAT JSON"""
    )
    operator_name_fetching_data = data_fetcher(operator_name_fetching_query).json().get("data")
    if operator_name_fetching_data:
        operator_name = operator_name_fetching_data[0].get("operator_name")
    else:
        raise HTTPException(status_code=400, detail="Operator name not found!")
    return operator_name


def validate_operator_id(operator_id: str):
    validate_operator_id_query = f"""
    SELECT _id FROM operator_meta WHERE _id = '{operator_id}' LIMIT 1 FORMAT JSON"""
    validate_operator_id = data_fetcher(validate_operator_id_query).json()
    if not validate_operator_id.get("data"):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST, detail="Please provide valid operator id"
        )
    return True


def on_iteration_start(callback_context: CallbackContext):
    schema = load_formatted_the_schema()
    callback_context.state["current_conversation"] = ""
    callback_context.state["validated_result"] = ""
    callback_context.state["generated_sql_query"] = ""
    callback_context.state["database_schema"] = schema
    return {}


def get_all_operators():
    query = "SELECT * FROM operator_meta FORMAT JSON"
    operator_data = data_fetcher(query)
    if operator_data.status_code == 200:
        operator_names = "\n".join(
            [operator_details.get("name") for operator_details in operator_data.json().get("data")]
        )
        return operator_names
    else:
        None


def extract_keys(data) -> Dict[str, Any]:
    result = {
        'response': None,
        'text': None,
        'agent_name': None,
        'author': None,
        'transfer_to_agent': None,
        'timestamp': None
    }

    try:
        print(f"Data type: {type(data)}")

        if hasattr(data, 'timestamp'):
            result['timestamp'] = data.timestamp
        if hasattr(data, 'author'):
            result['author'] = data.author

        if hasattr(data, 'content') and data.content is not None:
            content = data.content
            print("This content: ", content)

            if hasattr(content, 'parts') and content.parts:
                print("Processing parts...")
                for part in content.parts:
                    print("Processing part:", part)
                    if part:
                        if hasattr(part,
                                   'text') and part.text and result[
                                       'text'] is None:
                            result['text'] = part.text

                        if hasattr(part,
                                   'function_call') and part.function_call:
                            func_call = part.function_call
                            print(f"Function call found: {func_call.name}")

                            if func_call.name == 'transfer_to_agent':
                                if hasattr(func_call, 'args') and func_call.args:
                                    if hasattr(func_call.args, 'agent_name'):
                                        result[
                                            'agent_name'] = func_call.args.agent_name
                                    elif isinstance(
                                                    func_call.args,
                                                    dict) and 'agent_name' in func_call.args:
                                        result['agent_name'] = func_call.args[
                                            'agent_name']

                        if hasattr(part,
                                   'function_response') and part.function_response:
                            func_response = part.function_response
                            if hasattr(func_response, 'response'):
                                result['response'] = func_response.response

        if hasattr(data, 'actions') and data.actions is not None:
            actions = data.actions
            if isinstance(actions, dict):
                result['transfer_to_agent'] = actions.get('transfer_to_agent')
            elif hasattr(actions, 'transfer_to_agent'):
                result['transfer_to_agent'] = actions.transfer_to_agent

    except Exception as e:
        print(f"Error occurred: {str(e)}")
        print(f"Error type: {type(e)}")
        import traceback
        traceback.print_exc()

    return result


async def event_generator(user_id, session_id, user_question, agent,
                          temp_service, session, app_name) -> AsyncGenerator[
                              str, None]:
    try:
        response_content = types.Content(role="user", parts=[types.Part(
            text=user_question)])

        agent_event_generator = agent.run_async(
            user_id=user_id,
            session_id=session_id,
            new_message=response_content
        )
        async for event in agent_event_generator:
            temp_service.append_event(session, event)
            sse_data = extract_keys(event)
            sse_data = f"response: {json.dumps(sse_data)}\n\n"
            yield sse_data
            await asyncio.sleep(0.01)

    except Exception as e:
        error_data = {
            "content": f"An error occurred: {str(e)}",
        }
        yield f"data: {json.dumps(error_data)}\n\n"
    finally:
        if temp_service:
            try:
                await temp_service.delete_session(
                    app_name=app_name,
                    user_id=user_id,
                    session_id=session_id
                )
            except Exception as cleanup_error:
                print(f"Error cleaning up session after streaming: {str(cleanup_error)}")

    completion_data = {
        "type": "completion",
        "content": "Stream completed",
        "timestamp": None
    }
    yield f"data: {json.dumps(completion_data)}\n\n"
