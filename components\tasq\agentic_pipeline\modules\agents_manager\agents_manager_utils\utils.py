from google.genai import types
from google.adk.agents import LlmAgent
from google.adk.runners import Runner
from google.adk.runners import RunConfig


from components.tasq.agentic_pipeline.core.config import settings
from components.tasq.agentic_pipeline.core.constants import LLM_MODEL

logger = settings.LOGGER


def get_agent(
    name: str,
    prompt: str,
    output_key: str = None,
    tools: list = [],
    sub_agents: list = [],
    after_callback=None,
    before_callback=None,
) -> LlmAgent:
    agent = LlmAgent(name=name)
    agent.model = LLM_MODEL
    agent.instruction = prompt
    if output_key:
        agent.output_key = output_key
    if len(tools) != 0:
        agent.tools = tools
    if len(sub_agents) != 0:
        agent.sub_agents = sub_agents
    if after_callback:
        agent.after_agent_callback = after_callback
    if before_callback:
        agent.before_agent_callback = before_callback
    return agent


def ask_root(query: str, session_id: str, user_id: str, runner: Runner) -> str:
    response_content = types.Content(role="user", parts=[types.Part(text=query)])
    events = runner.run(user_id=user_id, session_id=session_id,
                        new_message=response_content)
    response = None
    for event in events:
        if event.is_final_response():
            response = event.content.parts[0].text
    if not response:
        response = "There was an error while executing agent"
    return response


def ask_root_sql(query: str, session_id: str, user_id: str, runner: Runner,
                 run_config: RunConfig) -> str:
    response_content = types.Content(role="user", parts=[types.Part(text=query)])
    events = runner.run(user_id=user_id, session_id=session_id,
                        new_message=response_content, run_config=run_config)
    response = None
    for event in events:
        if event.is_final_response():
            response = event.content.parts[0].text
    if not response:
        response = "There was an error while executing agent"
    return response
