from dataclasses import dataclass

from fastapi import HTTPException, Security, status
from fastapi.security import APIKeyQuery

from components.tasq.agentic_pipeline.core.config import settings

API_KEY_NAME = "access_token"
api_key_query = APIKeyQuery(name=API_KEY_NAME, auto_error=False)


@dataclass
class Context:
    is_admin: bool = False


async def get_server_api_key(api_key_query: str = Security(api_key_query)):
    if api_key_query is not None:
        token = api_key_query
    else:
        raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail="Could not validate credentials")

    if token == settings.API_KEY:
        context = Context(is_admin=True)
        return context

    raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail="Could not validate credentials")
