from urllib.parse import urlparse

from components.tasq.agentic_pipeline.database.abstract import BaseSessionManager


class SessionManagerFactory:
    """
    Factory class for creating session manager instances based on URI.
    """

    # Registry of supported database types and their manager classes
    _managers = {}

    @classmethod
    def register(cls, scheme: str, manager_class: type):
        """
        Register a manager class for a specific URI scheme.
        """
        cls._managers[scheme] = manager_class

    @classmethod
    def create_manager(cls, uri: str) -> BaseSessionManager:
        """
        Create a session manager instance based on the URI scheme.

        URI format examples:
        - shelve:///path/to/db/directory?session_file=sessions.db&message_file\
            =messages.db&user_file=users.db
        - **************************************************
        """
        parsed = urlparse(uri)
        scheme = parsed.scheme.lower()

        if scheme not in cls._managers:
            raise ValueError(f"Unsupported database type: {scheme}")

        manager_class = cls._managers[scheme]
        return manager_class.from_uri(uri)
