from abc import ABC, abstractmethod
from typing import Any, Dict, List, Optional


class BaseSessionManager(ABC):
    """
    Abstract base class for session managers.
    Defines the interface that all storage backends must implement.
    """

    @classmethod
    @abstractmethod
    def from_uri(cls, uri: str) -> "BaseSessionManager":
        """
        Create a session manager instance from a URI.
        URI format depends on the specific backend implementation.
        """
        pass

    @abstractmethod
    def create_user(self, user_id: str, user_data: Dict[str, Any] = None) -> None:
        """
        Persist or update a user record using user_id as primary key.
        Sets created_at if new and always updates updated_at.
        """
        pass

    @abstractmethod
    def get_user(self, user_id: str) -> Optional[Dict[str, Any]]:
        """
        Retrieve user record by its primary key (user_id).
        """
        pass

    @abstractmethod
    def create_message(self, author: str, text: str) -> str:
        """
        Persist a single message, return its unique ID, with timestamps.
        """
        pass

    @abstractmethod
    def get_messages(self, ids: List[str]) -> List[Dict[str, Any]]:
        """
        Retrieve full message records for given IDs.
        """
        pass

    @abstractmethod
    def create_session(
        self,
        session_id: str,
        user_id: str,
        app_name: str,
        operator_id: str,
        user_data: Dict[str, Any] = None,
    ) -> None:
        """
        Persist a new session: upserts user, creates messages,
        then links them in the sessions collection, with timestamps.
        """
        pass

    @abstractmethod
    def get_session(self, session_id: str) -> Optional[Dict[str, Any]]:
        """
        Retrieve a session by ID, and embed full user and messages.
        """
        pass

    @abstractmethod
    def update_session(
        self,
        session_id: str,
        messages: List[Dict[str, str]],
    ) -> bool:
        """
        Append new messages to an existing session and update its timestamp.
        """
        pass

    @abstractmethod
    def delete_session(self, session_id: str) -> bool:
        """
        Delete a session.
        """
        pass

    @abstractmethod
    def session_exists(self, session_id: str) -> bool:
        """
        Check if session exists.
        """
        pass

    @abstractmethod
    def get_user_id(self, session_id: str) -> Optional[str]:
        """
        Retrieve user_id for a session.
        """
        pass

    def _validate_messages(self, messages: Any) -> None:
        """
        Ensure messages is a list of dicts each with 'author' and 'text'.
        """
        if not isinstance(messages, list):
            raise ValueError("`messages` must be a list of dicts")
        for index, message in enumerate(messages):
            if not isinstance(message, dict):
                raise ValueError(f"Message at index {index} is not a dict")
            if "author" not in message or "text" not in message:
                raise ValueError(f"Message at index {index} must contain 'author' and 'text'")
