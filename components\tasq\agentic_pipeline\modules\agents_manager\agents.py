from google.adk.agents import LoopAgent, SequentialAgent
# from google.adk.agents import RunConfig, StreamingMode

from components.tasq.agentic_pipeline.core.config import settings
from components.tasq.agentic_pipeline.modules.agents_manager.agents_manager_utils.prompts import get_prompts
from components.tasq.agentic_pipeline.modules.agents_manager.agents_manager_utils.utils import get_agent
from components.tasq.agentic_pipeline.modules.tools_manager.tools import (
    detailed_data_tool,
    exit_loop,
    plot_data,
    signal_search_tool,
    sql_executor,
    sql_tool,
)
from components.tasq.agentic_pipeline.modules.tools_manager.tools_manager_utils.utils import (
    call_recommendation_signal_search,
)

MAX_ITER = settings.MAX_ITER
agent_prompts = get_prompts()


class GetAgents:

    def get_sql_gen_agent(self):
        sql_agent = get_agent(name="SQLAgent",
                              prompt=agent_prompts.get_sql_gen_prompt(),
                              output_key="generated_sql_query",
                              tools=[sql_tool])

        return sql_agent

    def get_sql_execution_agent(self):
        exec_agent = get_agent(name="SQLExecutionAgent",
                               prompt=agent_prompts.get_sql_exec_prompt(),
                               output_key="execution_results",
                               tools=[sql_executor])
        return exec_agent

    def get_sql_query_validator(self):
        return get_agent(name="QueryValidator",
                         prompt=agent_prompts.get_query_validator_prompt(),
                         output_key="validated_result",
                         tools=[exit_loop])

    def get_validated_query_responder_agent(self):
        return get_agent(name="QueryResponder",
                         prompt=agent_prompts.get_validated_sql_query_prompt(),
                         output_key="query_responder")

    def sql_data_validator_agent(self):
        validator_agent = get_agent(
            name="DataValidatorAgent",
            prompt=agent_prompts.get_val_agent_prompt(),
            output_key="validated_result",
            tools=[exit_loop])
        return validator_agent

    def error_analysis_agent(self):
        err_analysis_agent = get_agent(
            name="ErrorAnalysisAgent",
            prompt=agent_prompts.get_error_analysis_prompt(),
            output_key="error_analysis")
        return err_analysis_agent

    def analytics_agent(self):
        analytic_agent = get_agent(
            name="AnalyticsAgent",
            prompt=agent_prompts.get_analytics_agent_prompt(),
            tools=[plot_data],
            output_key="analytics_results")
        return analytic_agent

    def analysis_router_agent(self):
        error_agent = self.error_analysis_agent()
        analytic_agent = self.analytics_agent()

        analysis_router_agent = get_agent(
            name="AnalysisRoutingAgent",
            prompt=agent_prompts.get_analysis_route_prompt(),
            output_key="routing_result",
            sub_agents=[error_agent, analytic_agent])
        return analysis_router_agent

    def get_signal_search_agent(self):
        signal_search_agent = get_agent(
            name="SignalSearchAgent",
            prompt=agent_prompts.get_signal_trigger_prompt(),
            output_key="signal_search_result",
            tools=[signal_search_tool,
                   call_recommendation_signal_search])
        return signal_search_agent

    def get_well_shortcut_agent(self):
        return get_agent(name="RundownShortcutAgent",
                         prompt=agent_prompts.rundown_shortcut_prompt(),
                         output_key="detailed_well_data",
                         tools=[detailed_data_tool])

    def get_memory_agent(self):
        memory_agent = get_agent(name="MemoryAgent",
                                 prompt=agent_prompts.get_memory_prompt(),
                                 output_key="updated_user_question")
        return memory_agent

    def get_base_router_agent(self):
        sql_pipeline_agent = self.get_full_sql_pipeline_tool()
        signal_search_agent = self.get_signal_search_agent()
        well_shortcut_agent = self.get_well_shortcut_agent()

        routing_agent = get_agent(name="BaseRouterAgent",
                                  prompt=agent_prompts.get_routing_prompt(),
                                  sub_agents=[sql_pipeline_agent,
                                              signal_search_agent,
                                              well_shortcut_agent])
        return routing_agent

    def get_sql_data_agent(self, ):
        return get_agent(name="DataResponder",
                         prompt=agent_prompts.get_sql_data_agent_prompt(),
                         output_key="sql_data_response")

    def get_sql_loop_agent(self,):
        sql_gen = self.get_sql_gen_agent()
        sql_exec = self.get_sql_execution_agent()
        sql_val = self.sql_data_validator_agent()

        loop_pipeline = LoopAgent(
            name="LoopSqlPipeline",
            sub_agents=[sql_gen, sql_exec, sql_val],
            max_iterations=MAX_ITER)
        return loop_pipeline

    def get_full_sql_pipeline_tool(self):
        router = self.analysis_router_agent()
        loop_pipeline = self.get_sql_loop_agent()
        sequential_pipeline = SequentialAgent(
            name="FullSQLPipelineAgent",
            sub_agents=[loop_pipeline, router]
        )

        return sequential_pipeline

    def sql_dedicated_agent(self,):
        sql_loop_agent = self.get_sql_loop_agent()
        sql_data_agent = self.get_sql_data_agent()
        sql_main_agent = SequentialAgent(name="SqlPocAgent",
                                         sub_agents=[sql_loop_agent,
                                                     sql_data_agent])
        return sql_main_agent

    def sql_query_dedicated_agent(self, ):
        sql_query_agent = self.get_sql_gen_agent()
        sql_query_validator = self.get_sql_query_validator()

        sql_query_responder = self.get_validated_query_responder_agent()

        sql_loop_query_agent = LoopAgent(name='LoopQueryAgent',
                                         sub_agents=[sql_query_agent,
                                                     sql_query_validator],
                                         max_iterations=MAX_ITER)

        sql_query_responder_main = SequentialAgent(
            name="SqlQueryResponderMain",
            sub_agents=[
                sql_loop_query_agent,
                sql_query_responder])
        return sql_query_responder_main

    def simplified_sql_query_dedicated_agent(self, ):
        sql_query_agent = get_agent(name="SimplifiedSQLAgent",
                                      prompt=agent_prompts.get_simplified_sql_gen_prompt(),
                                      output_key="generated_sql_query",
                                      tools=[sql_tool])

        sql_query_responder = self.get_validated_query_responder_agent()

        sql_query_responder_main = SequentialAgent(
            name="SimplifiedSqlQueryResponderMain",
            sub_agents=[
                sql_query_agent,
                sql_query_responder])
        return sql_query_responder_main


# class OptGetAgents:
#     def __init__(self):
#         self.sql_run_config = RunConfig(
#             streaming_mode=StreamingMode.NONE,
#             max_llm_calls=50,
#             save_input_blobs_as_artifacts=False,
#             response_modalities=["TEXT"]
#         )

#         self.validation_run_config = RunConfig(
#             streaming_mode=StreamingMode.NONE,
#             max_llm_calls=20,
#             save_input_blobs_as_artifacts=False,
#             response_modalities=["TEXT"]
#         )

#         self.analysis_run_config = RunConfig(
#             streaming_mode=StreamingMode.NONE,
#             max_llm_calls=30,
#             save_input_blobs_as_artifacts=False,
#             response_modalities=["TEXT"]
#         )

#     def get_sql_gen_agent(self):
#         sql_agent = get_agent(
#             name="SQLAgent",
#             prompt=agent_prompts.get_sql_gen_prompt(),
#             output_key="generated_sql_query",
#             tools=[sql_tool]
#         )
#         sql_agent.run_config = self.sql_run_config
#         return sql_agent

#     def get_sql_execution_agent(self):
#         """SQL execution agent - minimal LLM usage needed"""
#         exec_agent = get_agent(
#             name="SQLExecutionAgent",
#             prompt=agent_prompts.get_sql_exec_prompt(),
#             output_key="execution_results",
#             tools=[sql_executor]
#         )
#         exec_agent.run_config = self.validation_run_config
#         return exec_agent

#     def sql_data_validator_agent(self):
#         validator_agent = get_agent(
#             name="DataValidatorAgent",
#             prompt=agent_prompts.get_val_agent_prompt(),
#             output_key="validated_result",
#             tools=[exit_loop]
#         )
#         validator_agent.run_config = self.validation_run_config
#         return validator_agent

#     def error_analysis_agent(self):
#         err_analysis_agent = get_agent(
#             name="ErrorAnalysisAgent",
#             prompt=agent_prompts.get_error_analysis_prompt(),
#             output_key="error_analysis"
#         )
#         err_analysis_agent.run_config = self.analysis_run_config
#         return err_analysis_agent

#     def analytics_agent(self):
#         analytic_agent = get_agent(
#             name="AnalyticsAgent",
#             prompt=agent_prompts.get_analytics_agent_prompt(),
#             tools=[plot_data],
#             output_key="analytics_results"
#         )
#         analytic_agent.run_config = self.sql_run_config
#         return analytic_agent

#     def analysis_router_agent(self):
#         analytic_agent = self.analytics_agent()
#         error_agent = self.error_analysis_agent()

#         analysis_router_agent = get_agent(
#             name="AnalysisRoutingAgent",
#             prompt=agent_prompts.get_analysis_route_prompt(),
#             output_key="routing_result",
#             sub_agents=[error_agent, analytic_agent]
#         )
#         router_config = RunConfig(
#             streaming_mode=StreamingMode.NONE,
#             max_llm_calls=10,
#             save_input_blobs_as_artifacts=False,
#             response_modalities=["TEXT"]
#         )
#         analysis_router_agent.run_config = router_config
#         return analysis_router_agent

#     def get_signal_search_agent(self):
#         signal_search_agent = get_agent(
#             name="SignalSearchAgent",
#             prompt=agent_prompts.get_signal_trigger_prompt(),
#             output_key="signal_search_result",
#             tools=[signal_search_tool, call_recommendation_signal_search]
#         )
#         signal_search_agent.run_config = self.sql_run_config
#         return signal_search_agent

#     def get_well_shortcut_agent(self):
#         agent = get_agent(
#             name="RundownShortcutAgent",
#             prompt=agent_prompts.rundown_shortcut_prompt(),
#             output_key="detailed_well_data",
#             tools=[detailed_data_tool]
#         )
#         agent.run_config = self.sql_run_config
#         return agent

#     def get_memory_agent(self):
#         memory_agent = get_agent(
#             name="MemoryAgent",
#             prompt=agent_prompts.get_memory_prompt(),
#             output_key="updated_user_question"
#         )
#         memory_agent.run_config = self.validation_run_config
#         return memory_agent

#     def get_base_router_agent(self):
#         sql_pipeline_agent = self.get_full_sql_pipeline_tool()
#         signal_search_agent = self.get_signal_search_agent()
#         well_shortcut_agent = self.get_well_shortcut_agent()

#         routing_agent = get_agent(
#             name="BaseRouterAgent",
#             prompt=agent_prompts.get_routing_prompt(),
#             sub_agents=[sql_pipeline_agent, signal_search_agent,
#                         well_shortcut_agent]
#         )
#         base_router_config = RunConfig(
#             streaming_mode=StreamingMode.NONE,
#             max_llm_calls=15,
#             save_input_blobs_as_artifacts=False,
#             response_modalities=["TEXT"]
#         )
#         routing_agent.run_config = base_router_config
#         return routing_agent

#     def get_sql_data_agent(self):
#         agent = get_agent(
#             name="DataResponder",
#             prompt=agent_prompts.get_sql_data_agent_prompt(),
#             output_key="sql_data_response"
#         )
#         agent.run_config = self.validation_run_config
#         return agent

#     def get_sql_loop_agent(self):
#         sql_gen = self.get_sql_gen_agent()
#         sql_exec = self.get_sql_execution_agent()
#         sql_val = self.sql_data_validator_agent()

#         loop_pipeline = LoopAgent(
#             name="LoopSqlPipeline",
#             sub_agents=[sql_gen, sql_exec, sql_val],
#             max_iterations=min(MAX_ITER, 3)
#         )

#         loop_config = RunConfig(
#             streaming_mode=StreamingMode.NONE,
#             max_llm_calls=100,
#             save_input_blobs_as_artifacts=False,
#             response_modalities=["TEXT"]
#         )
#         loop_pipeline.run_config = loop_config
#         return loop_pipeline

#     def get_full_sql_pipeline_tool(self):
#         router = self.analysis_router_agent()
#         loop_pipeline = self.get_sql_loop_agent()
#         sequential_pipeline = SequentialAgent(
#             name="FullSQLPipelineAgent",
#             sub_agents=[loop_pipeline, router]
#         )
#         pipeline_config = RunConfig(
#             streaming_mode=StreamingMode.NONE,
#             max_llm_calls=150,
#             save_input_blobs_as_artifacts=False,
#             response_modalities=["TEXT"]
#         )
#         sequential_pipeline.run_config = pipeline_config
#         return sequential_pipeline

#     def sql_dedicated_agent(self):
#         sql_loop_agent = self.get_sql_loop_agent()
#         sql_data_agent = self.get_sql_data_agent()
#         sql_main_agent = SequentialAgent(
#             name="SqlPocAgent",
#             sub_agents=[sql_loop_agent, sql_data_agent]
#         )
#         main_config = RunConfig(
#             streaming_mode=StreamingMode.NONE,
#             max_llm_calls=120,
#             save_input_blobs_as_artifacts=False,
#             response_modalities=["TEXT"]
#         )
#         sql_main_agent.run_config = main_config
#         return sql_main_agent


# # def create_high_performance_config() -> RunConfig:
# #     """Create RunConfig optimized for speed over features"""
# #     return RunConfig(
# #         streaming_mode=StreamingMode.NONE,  # Fastest mode
# #         max_llm_calls=25,  # Very restrictive
# #         save_input_blobs_as_artifacts=False,  # No extra I/O
# #         response_modalities=["TEXT"],  # Text only
# #         support_cfc=False  # Disable experimental features
# #     )


# # def create_balanced_config() -> RunConfig:
# #     return RunConfig(
# #         streaming_mode=StreamingMode.NONE,
# #         max_llm_calls=75,
# #         save_input_blobs_as_artifacts=False,
# #         response_modalities=["TEXT"]
# #     )


# # def apply_performance_config_to_agent(agent,
#       config_type: str = "balanced"):
# #     config = {
# #         "high_performance": create_high_performance_config(),
# #         "balanced": create_balanced_config(),
# #         "validation": RunConfig(
# #             streaming_mode=StreamingMode.NONE,
# #             max_llm_calls=20,
# #             save_input_blobs_as_artifacts=False,
# #             response_modalities=["TEXT"]
# #         )
# #     }

# #     if config_type in config:
# #         agent.run_config = config[config_type]
# #     return agent
